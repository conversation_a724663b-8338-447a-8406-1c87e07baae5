
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { getAirlineLogo, getAirlineName, getDisplayFlightNumber } from '@/app/lib/airlineHelpers';
import { Flight } from '../api/services/flightService';

interface FlightCardProps {
  flight: Flight;
  journeyType?: 'onward' | 'return';
  tripType?: 'oneWay' | 'roundTrip';
  compact?: boolean;
  selected?: boolean;
}

export default function FlightCard({ flight, journeyType = 'onward', tripType = 'oneWay', compact = false, selected = false }: FlightCardProps) {
  const router = useRouter();
  const [showDetails, setShowDetails] = useState(false);
  const [showFlightDetails, setShowFlightDetails] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [activeDetailsTab, setActiveDetailsTab] = useState('flight'); // For expanded details tabs
  const [activeFareSubTab, setActiveFareSubTab] = useState('changes'); // For fare sub-tabs
  const [selectedFareType, setSelectedFareType] = useState('saver');



  const handleShowFlightDetails = () => {
    setShowFlightDetails(true);
    setActiveTab('details');
  };

  const handleCloseFlightDetails = () => {
    setShowFlightDetails(false);
  };

  const handleBookNow = () => {
    const bookingParams = new URLSearchParams({
      flightId: flight.id.toString(),
      fareType: selectedFareType,
      price: flight.price.toString(),
      from: flight.departure.city,
      to: flight.arrival.city
    });

    router.push(`/flights/booking?${bookingParams.toString()}`);
  };







  return (
    <div className="relative">
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all ${selected ? 'border-[#013688] bg-blue-50' : ''} ${compact ? 'p-3' : 'p-4'}`}>
        {/* Main Flight Info - Compact Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <img
                src={getAirlineLogo(flight.airline, flight.logo)}
                alt={getAirlineName(flight.airline)}
                className="w-10 h-6 object-contain"
              />
            </div>
            <div>
              <div className="font-semibold text-gray-900 text-sm">{getAirlineName(flight.airline)}</div>
              <div className="text-gray-500 text-xs">{getDisplayFlightNumber(flight.airline, flight.flightNumber)}</div>
            </div>
          </div>
        </div>

        {/* Flight Times and Duration - Compact Layout */}
        <div className="grid grid-cols-5 gap-2 items-center mb-3">
          {/* Departure */}
          <div className="text-left">
            <div className="font-bold text-gray-900 text-xl">{flight.departure.time}</div>
            <div className="text-gray-600 text-sm">{flight.departure.city}</div>
            <div className="text-gray-500 text-xs">{flight.departure.airport}</div>
          </div>

          {/* Duration and Route */}
          <div className="col-span-3 text-center">
            <div className="text-gray-600 text-sm mb-1">{flight.duration}</div>
            <div className="flex items-center justify-center mb-1">
              <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
              <div className="flex-1 h-px bg-gray-300 mx-2 relative">
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <i className="ri-plane-line text-[#013688] text-sm"></i>
                </div>
              </div>
              <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
            </div>
            <div className="text-gray-500 text-xs">{flight.stops}</div>
          </div>

          {/* Arrival */}
          <div className="text-right">
            <div className="font-bold text-gray-900 text-xl">{flight.arrival.time}</div>
            <div className="text-gray-600 text-sm">{flight.arrival.city}</div>
            <div className="text-gray-500 text-xs">{flight.arrival.airport}</div>
          </div>
        </div>

        {/* Price and Action - Compact Bottom Section */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center space-x-4">
            <div>
              <div className="flex items-center space-x-2">
                <span className="font-bold text-gray-900 text-xl">₹{flight.price.toLocaleString()}</span>
                {flight.originalPrice && flight.originalPrice > flight.price && (
                  <span className="text-sm text-gray-500 line-through">₹{flight.originalPrice.toLocaleString()}</span>
                )}
              </div>
              {flight.originalPrice && flight.originalPrice > flight.price && (
                <div className="text-xs text-green-600 font-medium">
                  You save ₹{(flight.originalPrice - flight.price).toLocaleString()}
                </div>
              )}
            </div>
            <div className="text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <i className="ri-luggage-cart-line text-xs"></i>
                <span>{flight.baggage?.checkin || '15 kg'}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {!compact && (
              <>
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="text-[#013688] font-semibold hover:text-blue-700 transition-colors text-sm"
                >
                  {showDetails ? 'Hide Details' : 'Flight Details'}
                </button>
              </>
            )}
            {compact && selected && (
              <div className="flex items-center space-x-1 text-[#013688] font-medium text-sm">
                <i className="ri-check-line"></i>
                <span>Selected</span>
              </div>
            )}
            {tripType === 'oneWay' && (
              <button
                onClick={handleBookNow}
                className="bg-[#013688] text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-all text-sm"
              >
                Book Now
              </button>
            )}
          </div>
        </div>

        {/* Expanded Details with Tabs */}
          {showDetails && !compact && (
            <div className="mt-6 pt-6 border-t border-gray-100">
              {/* Tab Navigation */}
              <div className="border-b border-gray-200 mb-6">
                <nav className="flex">
                  <button
                    onClick={() => setActiveDetailsTab('flight')}
                    className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                      activeDetailsTab === 'flight'
                        ? 'border-[#013688] text-[#013688] bg-blue-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Flight Information
                  </button>
                  <button
                    onClick={() => setActiveDetailsTab('fare')}
                    className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                      activeDetailsTab === 'fare'
                        ? 'border-[#013688] text-[#013688] bg-blue-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Fare Summary & Rules
                  </button>
                  <button
                    onClick={() => setActiveDetailsTab('baggage')}
                    className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                      activeDetailsTab === 'baggage'
                        ? 'border-[#013688] text-[#013688] bg-blue-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Baggage Information
                  </button>
                </nav>
              </div>

              {/* Tab Content */}
              <div className="min-h-[200px]">
                {/* Flight Information Tab */}
                {activeDetailsTab === 'flight' && (
                  <div className="space-y-6">
                    {/* Route Header */}
                    <div className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                      <span>{flight.departure.city} {flight.arrival.city}</span>
                      <span className="text-sm font-normal text-gray-600">, {flight.departure.date}</span>
                    </div>

                    {/* Flight Details Card */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      {/* Airline Info Header */}
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center space-x-3">
                          <img
                            src={getAirlineLogo(flight.airline, flight.logo)}
                            alt={getAirlineName(flight.airline)}
                            className="w-12 h-8 object-contain"
                          />
                          <div>
                            <div className="font-semibold text-gray-900 text-lg">{getAirlineName(flight.airline)}</div>
                            <div className="text-sm text-gray-600">{getDisplayFlightNumber(flight.airline, flight.flightNumber)}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-600">Aircraft</div>
                          <div className="font-medium">Airbus A320</div>
                          <div className="text-sm text-gray-600 mt-2">Travel Class</div>
                          <div className="font-medium">Economy</div>
                        </div>
                      </div>

                      {/* Flight Timeline */}
                      <div className="flex items-start justify-between">
                        {/* Departure */}
                        <div className="text-left flex-1">
                          <div className="text-3xl font-bold text-gray-900 mb-1">{flight.departure?.time || '04:25'}</div>
                          <div className="text-sm text-gray-600 mb-1">Thu, 14 Aug 25</div>
                          <div className="font-medium text-gray-900 mb-1">{flight.departure.city} [{flight.departure.airport}]</div>
                          <div className="text-sm text-gray-600 mb-1">
                            Chhatrapati Shivaji International airport
                          </div>
                          <div className="text-sm text-gray-600 mb-1">|Mumbai |IN |India</div>
                          <div className="text-sm text-gray-600">Terminal 2</div>
                        </div>

                        {/* Duration */}
                        <div className="flex-1 text-center px-6 py-4">
                          <div className="text-sm text-gray-600 mb-2">{flight.duration || '2 Hr. 10 Min.'}</div>
                          <div className="flex items-center justify-center mb-2">
                            <div className="flex-1 border-t-2 border-dotted border-gray-300"></div>
                            <div className="mx-3">
                              <i className="ri-plane-line text-[#013688] text-2xl"></i>
                            </div>
                            <div className="flex-1 border-t-2 border-dotted border-gray-300"></div>
                          </div>
                          <div className="text-sm text-gray-600">0</div>
                        </div>

                        {/* Arrival */}
                        <div className="text-right flex-1">
                          <div className="text-3xl font-bold text-gray-900 mb-1">{flight.arrival?.time || '06:35'}</div>
                          <div className="text-sm text-gray-600 mb-1">Thu, 14 Aug 25</div>
                          <div className="font-medium text-gray-900 mb-1">{flight.arrival.city} [{flight.arrival.airport}]</div>
                          <div className="text-sm text-gray-600 mb-1">
                            Indira Gandhi International |New Delhi |IN
                          </div>
                          <div className="text-sm text-gray-600 mb-1">|India</div>
                          <div className="text-sm text-gray-600">Terminal 3</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Fare Summary & Rules Tab */}
                {activeDetailsTab === 'fare' && (
                  <div className="space-y-4">
                    {/* Sub-tabs for Fare */}
                    <div className="border-b border-gray-200">
                      <nav className="flex space-x-8">
                        <button
                          onClick={() => setActiveFareSubTab('changes')}
                          className={`py-2 px-1 text-sm font-medium border-b-2 transition-colors ${
                            activeFareSubTab === 'changes'
                              ? 'border-red-500 text-[#013688]'
                              : 'border-transparent text-gray-500 hover:text-gray-700'
                          }`}
                        >
                          CHANGES/REISSUE
                        </button>
                        <button
                          onClick={() => setActiveFareSubTab('cancel')}
                          className={`py-2 px-1 text-sm font-medium border-b-2 transition-colors ${
                            activeFareSubTab === 'cancel'
                              ? 'border-red-500 text-[#013688]'
                              : 'border-transparent text-gray-500 hover:text-gray-700'
                          }`}
                        >
                          CANCEL PENALTY
                        </button>
                        <button
                          onClick={() => setActiveFareSubTab('service')}
                          className={`py-2 px-1 text-sm font-medium border-b-2 transition-colors ${
                            activeFareSubTab === 'service'
                              ? 'border-red-500 text-[#013688]'
                              : 'border-transparent text-gray-500 hover:text-gray-700'
                          }`}
                        >
                          ATO SERVICE FEE
                        </button>
                      </nav>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Left Column - Sub-tab Content */}
                      <div>
                        <div className="text-sm font-medium text-gray-900 mb-4">{flight.departure.city} - {flight.arrival.city}</div>

                        {activeFareSubTab === 'changes' && (
                          <div className="bg-gray-50 rounded-lg overflow-hidden">
                            <div className="bg-gray-100 px-4 py-2 flex justify-between text-sm font-medium">
                              <span>CHANGES/REISSUE</span>
                              <span>Adult</span>
                            </div>
                            <div className="p-4 space-y-3">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Before</span>
                                <span className="font-medium">₹ 3000</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">After</span>
                                <span className="font-medium">Non Changeable</span>
                              </div>
                            </div>
                          </div>
                        )}

                        {activeFareSubTab === 'cancel' && (
                          <div className="bg-gray-50 rounded-lg overflow-hidden">
                            <div className="bg-gray-100 px-4 py-2 flex justify-between text-sm font-medium">
                              <span>CANCEL PENALTY</span>
                              <span>Adult</span>
                            </div>
                            <div className="p-4 space-y-3">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Before</span>
                                <span className="font-medium">₹ 3500</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">After</span>
                                <span className="font-medium">Non Refundable</span>
                              </div>
                            </div>
                          </div>
                        )}

                        {activeFareSubTab === 'service' && (
                          <div className="bg-gray-50 rounded-lg overflow-hidden">
                            <div className="bg-gray-100 px-4 py-2 flex justify-between text-sm font-medium">
                              <span>ATO SERVICE FEE</span>
                              <span>Adult</span>
                            </div>
                            <div className="p-4 space-y-3">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Service Fee</span>
                                <span className="font-medium">₹ 250</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Processing Fee</span>
                                <span className="font-medium">₹ 150</span>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Disclaimer */}
                        <div className="mt-6 space-y-3 text-xs text-gray-600">
                          <p>• The above data is indicatory , fare rules are subject to changes by the Airline from time to time depending upon Fare class and change/cancellation fee amount may also vary based on fluctuations in currency conversion rates.</p>
                          <p>• Although we will try to keep this section updated regularly.</p>
                          <p>• Feel free to call our Contact 'Centre for exact cancellation/change fee.</p>
                          <p>• Cancellation/Date change request will be accepted 30 hrs prior to departure.</p>
                          <p>• GST + RAF charges applicable on cancellation/Reissue penalty.</p>
                        </div>
                      </div>

                      {/* Right Column - Fare Details */}
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-4">
                          <h4 className="font-semibold text-gray-900">Fare Details</h4>
                          <span className="text-sm text-[#013688] font-medium">1 Traveller</span>
                        </div>
                        <div className="space-y-3 text-sm">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center space-x-2">
                              <div className="w-4 h-4 border border-gray-400 rounded flex items-center justify-center">
                                <span className="text-xs">+</span>
                              </div>
                              <span className="text-gray-700">Base Fare</span>
                            </div>
                            <span className="font-medium">₹ {Math.round(flight.price * 0.75)}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="flex items-center space-x-2">
                              <div className="w-4 h-4 border border-gray-400 rounded flex items-center justify-center">
                                <span className="text-xs">+</span>
                              </div>
                              <span className="text-gray-700">Tax & Charges</span>
                            </div>
                            <span className="font-medium">₹ {Math.round(flight.price * 0.25)}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="flex items-center space-x-2">
                              <div className="w-4 h-4 bg-green-500 rounded flex items-center justify-center">
                                <i className="ri-check-line text-white text-xs"></i>
                              </div>
                              <span className="text-green-600">Discount Applied</span>
                            </div>
                            <span className="font-medium text-green-600">₹ 260</span>
                          </div>
                          <div className="border-t border-gray-300 pt-3 mt-4">
                            <div className="flex justify-between items-center">
                              <span className="font-semibold text-gray-900">Total Amount:</span>
                              <span className="font-bold text-lg">₹ {flight.price.toLocaleString()}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Baggage Information Tab */}
                {activeDetailsTab === 'baggage' && (
                  <div className="space-y-6">
                    {/* Baggage Table */}
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="grid grid-cols-3 bg-gray-50">
                        <div className="px-6 py-4 text-center font-medium text-gray-900 border-r border-gray-200">
                          Sector/Flight
                        </div>
                        <div className="px-6 py-4 text-center font-medium text-gray-900 border-r border-gray-200">
                          Check in Baggage
                        </div>
                        <div className="px-6 py-4 text-center font-medium text-gray-900">
                          Cabin Baggage
                        </div>
                      </div>
                      <div className="grid grid-cols-3 border-t border-gray-200">
                        <div className="px-6 py-4 text-center text-gray-900 border-r border-gray-200">
                          {flight.departure.city} - {flight.arrival.city}
                        </div>
                        <div className="px-6 py-4 text-center text-gray-900 border-r border-gray-200">
                          {flight.baggage?.checkin || '15 Kg'} (Adult)
                        </div>
                        <div className="px-6 py-4 text-center text-gray-900">
                          {flight.baggage?.cabin || '7 Kg'} (Adult)
                        </div>
                      </div>
                    </div>

                    {/* Baggage Information Notes */}
                    <div className="space-y-4 text-sm text-gray-600">
                      <p>• The information presented above is as obtained from the airline reservation system. Akbartravels.com does not guarantee the accuracy of this information.</p>
                      <p>• The baggage allowance may vary according to stop-overs, connecting flights and changes in airline rules.</p>
                    </div>

                    {/* Warning Alert */}
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          <i className="ri-alert-line text-red-500 text-lg"></i>
                        </div>
                        <div className="text-sm text-red-700">
                          <strong>Adding of additional baggage is subject to load factor of the flight. Incase if baggage could not be added, payment for the additional baggage paid will be reverted.</strong>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

        {/* Show flight details button in compact view */}
        {compact && (
          <div className="text-end">
            <button
              onClick={handleShowFlightDetails}
              className="text-primary underline text-sm"
            >
              Flight Details
            </button>
          </div>
        )}
      </div>

      {/* Right-to-Left Flight Details Popup */}
      {showFlightDetails && (
        <div className="fixed inset-0 bg-black/50 z-[200] flex items-center justify-end">
          <div className={`bg-white h-full w-full max-w-lg shadow-2xl transform transition-transform duration-300 overflow-y-auto ${showFlightDetails ? 'translate-x-0' : 'translate-x-full'}`}>
            {/* Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
              <h2 className="text-lg font-bold text-gray-900">Flight Details</h2>
              <button
                onClick={handleCloseFlightDetails}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <i className="ri-close-line text-xl text-gray-500"></i>
              </button>
            </div>

            {/* Flight Summary */}
            <div className="p-4 border-b border-gray-100 bg-gray-50">
              <div className="flex items-center space-x-3 mb-3">
                <img src={getAirlineLogo(flight.airline, flight.logo)} alt={getAirlineName(flight.airline)} className="w-12 h-8 object-contain" />
                <div>
                  <div className="font-semibold text-gray-900">{getAirlineName(flight.airline)}</div>
                  <div className="text-sm text-gray-500">{getDisplayFlightNumber(flight.airline, flight.flightNumber)}</div>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <div>
                  <div className="font-bold text-lg">{flight.departure.time}</div>
                  <div className="text-gray-600">{flight.departure.city} ({flight.departure.airport})</div>
                </div>
                <div className="text-center px-4">
                  <div className="text-gray-600 mb-1">{flight.duration}</div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
                    <div className="flex-1 h-0.5 bg-gray-300 relative min-w-[60px]">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <i className="ri-plane-line text-[#013688]"></i>
                      </div>
                    </div>
                    <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">{flight.stops}</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg">{flight.arrival.time}</div>
                  <div className="text-gray-600">{flight.arrival.city} ({flight.arrival.airport})</div>
                </div>
              </div>
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex">
                <button
                  onClick={() => setActiveTab('details')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${activeTab === 'details' ? 'border-[#013688] text-[#013688] bg-blue-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  Flight Details
                </button>
                <button
                  onClick={() => setActiveTab('baggage')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${activeTab === 'baggage' ? 'border-[#013688] text-[#013688] bg-blue-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  Baggage
                </button>
                <button
                  onClick={() => setActiveTab('fare')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${activeTab === 'fare' ? 'border-[#013688] text-[#013688] bg-blue-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  Fare & Rules
                </button>
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {/* Flight Details Tab */}
              {activeTab === 'details' && (
                <div className="space-y-6">
                  {/* Flight Information */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">Flight Information</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="text-gray-600">Aircraft Type</div>
                          <div className="font-medium">Airbus A320</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Flight Number</div>
                          <div className="font-medium">{getDisplayFlightNumber(flight.airline, flight.flightNumber)}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Total Distance</div>
                          <div className="font-medium">1,740 km</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Flight Duration</div>
                          <div className="font-medium">{flight.duration}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">On-time Performance</div>
                          <div className="font-medium text-green-600">87%</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Stops</div>
                          <div className="font-medium">{flight.stops}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Route Details */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">Route Details</h3>
                    <div className="space-y-4">
                      {/* Departure */}
                      <div className="flex items-start space-x-4 bg-gray-50 rounded-lg p-4">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <i className="ri-takeoff-line text-green-600"></i>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-semibold text-gray-900">{flight.departure.city}</div>
                            <div className="text-lg font-bold text-gray-900">{flight.departure.time}</div>
                          </div>
                          <div className="text-sm text-gray-600">{flight.departure.airport} Airport</div>
                          <div className="text-sm text-gray-500">Terminal 3 • Gate B12</div>
                        </div>
                      </div>

                      {/* Connection (if any) */}
                      {flight.stops !== 'Non-stop' && (
                        <div className="flex items-start space-x-4 bg-yellow-50 rounded-lg p-4">
                          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <i className="ri-time-line text-yellow-600"></i>
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-gray-900 mb-1">Connection at Mumbai</div>
                            <div className="text-sm text-gray-600">Layover time: 1 hour 30 minutes</div>
                            <div className="text-sm text-gray-500">Terminal change required</div>
                          </div>
                        </div>
                      )}

                      {/* Arrival */}
                      <div className="flex items-start space-x-4 bg-gray-50 rounded-lg p-4">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <i className="ri-plane-landing-line text-blue-600"></i>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-semibold text-gray-900">{flight.arrival.city}</div>
                            <div className="text-lg font-bold text-gray-900">{flight.arrival.time}</div>
                          </div>
                          <div className="text-sm text-gray-600">{flight.arrival.airport} International Airport</div>
                          <div className="text-sm text-gray-500">Terminal 1 • Arrival Gate A5</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Amenities */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">In-flight Amenities</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {flight.features.map((feature: string, index: number) => (
                        <div key={index} className="flex items-center space-x-2 bg-green-50 rounded-lg p-3">
                          <i className="ri-check-line text-green-600"></i>
                          <span className="text-sm font-medium text-gray-800">{feature}</span>
                        </div>
                      ))}
                      <div className="flex items-center space-x-2 bg-green-50 rounded-lg p-3">
                        <i className="ri-check-line text-green-600"></i>
                        <span className="text-sm font-medium text-gray-800">Seat Selection</span>
                      </div>
                      <div className="flex items-center space-x-2 bg-green-50 rounded-lg p-3">
                        <i className="ri-check-line text-green-600"></i>
                        <span className="text-sm font-medium text-gray-800">Priority Boarding</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Baggage Tab */}
              {activeTab === 'baggage' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">Baggage Allowance</h3>

                    {/* Cabin Baggage */}
                    <div className="bg-blue-50 rounded-lg p-4 mb-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <i className="ri-suitcase-line text-blue-600"></i>
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-gray-900 mb-2">Cabin Baggage</div>
                          <div className="text-sm text-gray-700 mb-2">7 kg per passenger</div>
                          <div className="text-xs text-gray-600">
                            <div>• Maximum dimensions: 55 x 35 x 25 cm</div>
                            <div>• Must fit in overhead compartment</div>
                            <div>• No liquids over 100ml</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Check-in Baggage */}
                    <div className="bg-green-50 rounded-lg p-4 mb-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <i className="ri-luggage-cart-line text-green-600"></i>
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-gray-900 mb-2">Check-in Baggage</div>
                          <div className="text-sm text-gray-700 mb-2">{flight.baggage?.checkin || '15 kg'} per passenger</div>
                          <div className="text-xs text-gray-600">
                            <div>• Maximum weight per piece: {flight.baggage?.checkin || '15 kg'}</div>
                            <div>• Maximum dimensions: 158 cm (L+W+H)</div>
                            <div>• Additional baggage charges apply for excess weight</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Personal Item */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <i className="ri-handbag-line text-gray-600"></i>
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-gray-900 mb-2">Personal Item</div>
                          <div className="text-sm text-gray-700 mb-2">Small bag or purse</div>
                          <div className="text-xs text-gray-600">
                            <div>• Must fit under the seat in front of you</div>
                            <div>• Maximum dimensions: 40 x 30 x 15 cm</div>
                            <div>• No weight restriction</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Excess Baggage */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">Excess Baggage Charges</h3>
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="text-sm space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-700">Additional 5 kg</span>
                          <span className="font-semibold text-gray-900">₹1,500</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-700">Additional 10 kg</span>
                          <span className="font-semibold text-gray-900">₹2,800</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-700">Per kg (above 10 kg extra)</span>
                          <span className="font-semibold text-gray-900">₹350</span>
                        </div>
                      </div>
                      <div className="mt-3 pt-3 border-t border-red-200">
                        <div className="text-xs text-red-600">
                          <i className="ri-information-line mr-1"></i>
                          Pre-book excess baggage online to save up to 50% on airport charges
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Restricted Items */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">Restricted Items</h3>
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="text-sm text-gray-700">
                        <div className="font-medium mb-2">Items not allowed in cabin baggage:</div>
                        <ul className="space-y-1 text-xs">
                          <li>• Sharp objects (knives, scissors, razors)</li>
                          <li>• Liquids over 100ml</li>
                          <li>• Flammable items</li>
                          <li>• Lithium batteries (over 100Wh)</li>
                          <li>• Sporting goods</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Fare & Rules Tab */}
              {activeTab === 'fare' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">Fare Breakdown</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Base Fare (1 Adult)</span>
                          <span className="font-medium">₹{Math.round(flight.price * 0.75).toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Fuel Surcharge</span>
                          <span className="font-medium">₹{Math.round(flight.price * 0.08).toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Airport Taxes & Fees</span>
                          <span className="font-medium">₹{Math.round(flight.price * 0.12).toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Service Charges</span>
                          <span className="font-medium">₹{Math.round(flight.price * 0.05).toLocaleString()}</span>
                        </div>
                        <div className="border-t border-gray-300 pt-2 flex justify-between">
                          <span className="font-semibold text-gray-900">Total Amount</span>
                          <span className="font-bold text-lg text-[#013688]">₹{flight.price.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>

                    {/* Savings Information */}
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <i className="ri-price-tag-3-line text-green-600"></i>
                        <span className="font-semibold text-green-800">You Save</span>
                      </div>
                      <div className="text-sm space-y-1">
                        <div className="flex justify-between">
                          <span className="text-gray-700">Original Price</span>
                          <span className="line-through text-gray-500">₹{(flight.originalPrice || flight.price).toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-700">Discounted Price</span>
                          <span className="font-semibold text-green-700">₹{flight.price.toLocaleString()}</span>
                        </div>
                        {flight.originalPrice && flight.originalPrice > flight.price && (
                          <div className="flex justify-between text-green-600 font-semibold">
                            <span>Total Savings</span>
                            <span>₹{(flight.originalPrice - flight.price).toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Cancellation Policy */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">Cancellation Policy</h3>
                    <div className="space-y-4">
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="font-medium text-red-800 mb-2">
                          <i className="ri-close-circle-line mr-2"></i>
                          Cancellation Charges
                        </div>
                        <div className="text-sm text-gray-700 space-y-2">
                          <div className="flex justify-between">
                            <span>More than 7 days before departure</span>
                            <span className="font-medium">₹3,500 + Airline charges</span>
                          </div>
                          <div className="flex justify-between">
                            <span>3-7 days before departure</span>
                            <span className="font-medium">₹4,500 + Airline charges</span>
                          </div>
                          <div className="flex justify-between">
                            <span>0-3 days before departure</span>
                            <span className="font-medium">Non-refundable</span>
                          </div>
                        </div>
                        <div className="mt-3 pt-3 border-t border-red-200 text-xs text-red-600">
                          {flight.cancellation}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Date Change Policy */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">Date Change Policy</h3>
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                      <div className="font-medium text-orange-800 mb-2">
                        <i className="ri-calendar-line mr-2"></i>
                        Date Change Charges
                      </div>
                      <div className="text-sm text-gray-700 space-y-2">
                        <div className="flex justify-between">
                          <span>More than 7 days before departure</span>
                          <span className="font-medium">₹2,250 + Fare difference</span>
                        </div>
                        <div className="flex justify-between">
                          <span>3-7 days before departure</span>
                          <span className="font-medium">₹3,250 + Fare difference</span>
                        </div>
                        <div className="flex justify-between">
                          <span>0-3 days before departure</span>
                          <span className="font-medium">₹4,250 + Fare difference</span>
                        </div>
                      </div>
                      <div className="mt-3 pt-3 border-t border-orange-200 text-xs text-orange-600">
                        <i className="ri-information-line mr-1"></i>
                        Date changes are subject to seat availability and fare difference
                      </div>
                    </div>
                  </div>

                  {/* Terms & Conditions */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4">Important Terms</h3>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="text-sm text-gray-700 space-y-2">
                        <div>• Passengers must arrive at airport 2 hours before domestic flights</div>
                        <div>• Valid photo ID required for all passengers</div>
                        <div>• Web check-in opens 48 hours before departure</div>
                        <div>• Seat selection charges may apply</div>
                        <div>• Meal preferences can be selected during booking</div>
                        <div>• All prices are inclusive of applicable taxes</div>
                        <div>• Fare rules are subject to change without notice</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
