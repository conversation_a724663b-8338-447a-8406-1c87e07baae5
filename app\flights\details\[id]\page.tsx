'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { flightService, Flight, FlightDetailsResponse } from '@/app/api/services/flightService';
import { getAirlineLogo, getAirlineName, getDisplayFlightNumber } from '@/app/utils/airlineUtils';

export default function FlightDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const flightId = params.id as string;

  const [flightDetails, setFlightDetails] = useState<Flight | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFareType, setSelectedFareType] = useState('SAVER');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const loadFlightDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('🔍 Loading flight details for ID:', flightId);
        const response: FlightDetailsResponse = await flightService.getFlightDetails(flightId);
        
        if (response.success && response.data) {
          setFlightDetails(response.data);
          console.log('✅ Flight details loaded:', response.data);
        } else {
          throw new Error(response.message || 'Failed to load flight details');
        }
      } catch (err) {
        console.error('❌ Error loading flight details:', err);
        setError(err instanceof Error ? err.message : 'Failed to load flight details');
      } finally {
        setLoading(false);
      }
    };

    if (flightId) {
      loadFlightDetails();
    }
  }, [flightId]);

  const handleBookNow = () => {
    if (flightDetails) {
      // Save flight details to localStorage for booking page
      const bookingData = {
        flight: flightDetails,
        bookingDetails: {
          tui: `FLIGHT_DETAILS_${flightId}`,
          selectedFareType: selectedFareType
        }
      };
      
      localStorage.setItem('selectedFlightBooking', JSON.stringify(bookingData));
      console.log('✅ Flight details saved for booking:', bookingData);
      
      // Navigate to booking page
      router.push('/flights/booking');
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="pt-20 pb-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="animate-spin w-12 h-12 border-4 border-[#013688] border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-gray-600 text-lg">Loading flight details...</p>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !flightDetails) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="pt-20 pb-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="ri-error-warning-line text-2xl text-red-500"></i>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Flight Details</h2>
                <p className="text-gray-600 mb-6">{error || 'Flight details not found'}</p>
                <button
                  onClick={handleGoBack}
                  className="bg-[#013688] text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="pt-20 pb-16 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Back Button */}
          <div className="mb-6">
            <button
              onClick={handleGoBack}
              className="flex items-center space-x-2 text-[#013688] hover:text-blue-700 transition-colors"
            >
              <i className="ri-arrow-left-line text-xl"></i>
              <span className="font-medium">Back to Search Results</span>
            </button>
          </div>

          {/* Flight Header */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <img
                  src={getAirlineLogo(flightDetails.airline, flightDetails.logo)}
                  alt={getAirlineName(flightDetails.airline)}
                  className="w-16 h-10 object-contain"
                />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {flightDetails.departure.city} → {flightDetails.arrival.city}
                  </h1>
                  <p className="text-gray-600">
                    {getAirlineName(flightDetails.airline)} • {getDisplayFlightNumber(flightDetails.airline, flightDetails.flightNumber)}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-[#013688]">₹{flightDetails.price.toLocaleString()}</div>
                {flightDetails.originalPrice && flightDetails.originalPrice > flightDetails.price && (
                  <div className="text-sm text-gray-500 line-through">₹{flightDetails.originalPrice.toLocaleString()}</div>
                )}
                <div className="text-sm text-gray-600">per person</div>
              </div>
            </div>

            {/* Flight Route */}
            <div className="flex items-center justify-between bg-gray-50 rounded-lg p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{flightDetails.departure.time}</div>
                <div className="text-sm text-gray-600">{flightDetails.departure.airport}</div>
                <div className="text-sm text-gray-500">{flightDetails.departure.city}</div>
                {flightDetails.departure.terminal && (
                  <div className="text-xs text-gray-400">{flightDetails.departure.terminal}</div>
                )}
              </div>
              
              <div className="flex-1 mx-8">
                <div className="flex items-center justify-center">
                  <div className="flex-1 border-t-2 border-gray-300"></div>
                  <div className="mx-4 text-center">
                    <i className="ri-plane-line text-2xl text-[#013688] mb-1"></i>
                    <div className="text-sm text-gray-600">{flightDetails.duration}</div>
                    <div className="text-xs text-gray-500">{flightDetails.stops}</div>
                  </div>
                  <div className="flex-1 border-t-2 border-gray-300"></div>
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{flightDetails.arrival.time}</div>
                <div className="text-sm text-gray-600">{flightDetails.arrival.airport}</div>
                <div className="text-sm text-gray-500">{flightDetails.arrival.city}</div>
                {flightDetails.arrival.terminal && (
                  <div className="text-xs text-gray-400">{flightDetails.arrival.terminal}</div>
                )}
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="border-b border-gray-200">
              <nav className="flex">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`flex-1 py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'overview'
                      ? 'border-[#013688] text-[#013688] bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Flight Overview
                </button>
                <button
                  onClick={() => setActiveTab('fares')}
                  className={`flex-1 py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'fares'
                      ? 'border-[#013688] text-[#013688] bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Fare Options
                </button>
                <button
                  onClick={() => setActiveTab('details')}
                  className={`flex-1 py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'details'
                      ? 'border-[#013688] text-[#013688] bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Flight Details
                </button>
                <button
                  onClick={() => setActiveTab('policies')}
                  className={`flex-1 py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'policies'
                      ? 'border-[#013688] text-[#013688] bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Policies
                </button>
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                {/* Flight Overview Tab */}
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Flight Information</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-sm text-gray-600">Aircraft Type</div>
                          <div className="font-medium">{flightDetails.detailedInfo?.aircraft?.type || 'Airbus A320'}</div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-sm text-gray-600">Flight Duration</div>
                          <div className="font-medium">{flightDetails.duration}</div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-sm text-gray-600">Distance</div>
                          <div className="font-medium">{flightDetails.detailedInfo?.route?.distance || '1,740 km'}</div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-sm text-gray-600">Stops</div>
                          <div className="font-medium">{flightDetails.stops}</div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Baggage Information</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-sm text-gray-600">Cabin Baggage</div>
                          <div className="font-medium">{flightDetails.baggage.cabin}</div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-sm text-gray-600">Check-in Baggage</div>
                          <div className="font-medium">{flightDetails.baggage.checkin}</div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Features & Amenities</h3>
                      <div className="grid grid-cols-2 gap-2">
                        {flightDetails.features.map((feature, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <i className="ri-check-line text-green-500"></i>
                            <span className="text-gray-700">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Fare Options Tab */}
                {activeTab === 'fares' && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Your Fare</h3>
                    {flightDetails.fares.map((fare, index) => (
                      <div
                        key={index}
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                          selectedFareType === fare.fareType
                            ? 'border-[#013688] bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedFareType(fare.fareType)}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <input
                              type="radio"
                              checked={selectedFareType === fare.fareType}
                              onChange={() => setSelectedFareType(fare.fareType)}
                              className="text-[#013688]"
                            />
                            <div>
                              <div className="font-semibold text-gray-900">{fare.fareType}</div>
                              <div className="text-sm text-gray-600">{fare.benefits?.title}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-xl font-bold text-[#013688]">₹{fare.price.toLocaleString()}</div>
                            {fare.originalPrice && fare.originalPrice > fare.price && (
                              <div className="text-sm text-gray-500 line-through">₹{fare.originalPrice.toLocaleString()}</div>
                            )}
                          </div>
                        </div>

                        {fare.benefits && (
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            {fare.benefits.items.map((benefit, benefitIndex) => (
                              <div key={benefitIndex} className="flex items-center space-x-2">
                                <i className="ri-check-line text-green-500 text-xs"></i>
                                <span className="text-gray-600">{benefit}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {/* Flight Details Tab */}
                {activeTab === 'details' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Aircraft Information</h3>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div className="text-sm text-gray-600">Aircraft Model</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.aircraft?.type || 'Airbus A320'}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Seat Configuration</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.aircraft?.configuration || '3-3'}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Total Seats</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.aircraft?.totalSeats || 180}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Amenities</div>
                            <div className="font-medium">
                              {flightDetails.detailedInfo?.aircraft?.amenities?.join(', ') || 'Wi-Fi, Entertainment, USB Charging'}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Services</h3>
                      <div className="space-y-4">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="font-medium text-gray-900 mb-2">Meals & Beverages</div>
                          <p className="text-gray-600 text-sm">
                            {flightDetails.detailedInfo?.services?.meals?.type || 'Complimentary snacks and beverages'}
                          </p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="font-medium text-gray-900 mb-2">Entertainment</div>
                          <p className="text-gray-600 text-sm">
                            {flightDetails.detailedInfo?.services?.entertainment?.type || 'Streaming entertainment via personal device'}
                          </p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="font-medium text-gray-900 mb-2">Accessibility</div>
                          <p className="text-gray-600 text-sm">
                            {flightDetails.detailedInfo?.services?.accessibility?.specialNeeds || 'Special assistance available on request'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Policies Tab */}
                {activeTab === 'policies' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Check-in Policy</h3>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="grid grid-cols-1 gap-3">
                          <div>
                            <div className="text-sm text-gray-600">Domestic Flights</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.policies?.checkinTime?.domestic || '2 hours before departure'}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Web Check-in</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.policies?.checkinTime?.webCheckin || '48 hours to 2 hours before'}</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Baggage Policy</h3>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="space-y-3">
                          <div>
                            <div className="text-sm text-gray-600">Cabin Baggage</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.policies?.baggagePolicy?.cabin || '7 kg, dimensions: 55cm x 35cm x 25cm'}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Check-in Baggage</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.policies?.baggagePolicy?.checkin || '15 kg for Saver, 20 kg for Flexi, 30 kg for Business'}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Excess Baggage</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.policies?.baggagePolicy?.excessBaggage || '₹600 per kg'}</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancellation Policy</h3>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="space-y-3">
                          <div>
                            <div className="text-sm text-gray-600">Saver Fare</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.policies?.cancellation?.saver || 'Free till 2 hours before departure'}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Flexi Fare</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.policies?.cancellation?.flexi || 'Free till 24 hours before departure'}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Business Fare</div>
                            <div className="font-medium">{flightDetails.detailedInfo?.policies?.cancellation?.business || 'Free cancellation anytime'}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Sidebar - Booking Section */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-24">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Book This Flight</h3>
                
                <div className="space-y-4 mb-6">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Base Fare</span>
                    <span className="font-medium">₹{flightDetails.price.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Taxes & Fees</span>
                    <span className="font-medium">Included</span>
                  </div>
                  <div className="border-t pt-4">
                    <div className="flex justify-between">
                      <span className="text-lg font-semibold">Total</span>
                      <span className="text-lg font-bold text-[#013688]">₹{flightDetails.price.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={handleBookNow}
                  className="w-full bg-[#013688] text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors mb-4"
                >
                  Book Now
                </button>

                <div className="text-xs text-gray-500 text-center">
                  Free cancellation till 2 hours before departure
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
