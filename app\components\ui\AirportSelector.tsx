'use client';

import { useState, useRef, useEffect } from 'react';
import { airportService, AirportSelectorData } from '../app/api/services/airportService';

// Fallback airports in case API fails
const fallbackAirports: AirportSelectorData[] = [
  // Indian Airports
  { code: 'DEL', name: 'Delhi', fullName: 'DEL, Delhi Airport India' },
  { code: 'BLR', name: 'Bengaluru', fullName: 'BLR, Bengaluru International Airport' },
  { code: 'BOM', name: 'Mumbai', fullName: 'BOM, Chhatrapati Shivaji Mumbai International Airport' },
  { code: 'MAA', name: 'Chennai', fullName: 'MAA, Chennai International Airport' },
  { code: 'CCU', name: 'Kolkata', fullName: 'CCU, Netaji Subhash Chandra Bose International Airport' },
  { code: 'HYD', name: 'Hyderabad', fullName: 'HYD, Rajiv Gandhi International Airport' },
  { code: 'AMD', name: 'Ahmedabad', fullName: 'AMD, Sardar <PERSON>i <PERSON> International Airport' },
  { code: 'PNQ', name: 'Pune', fullName: 'PNQ, Pune Airport' },
  { code: 'GOI', name: 'Goa', fullName: 'GOI, Goa International Airport' },
  { code: 'JAI', name: 'Jaipur', fullName: 'JAI, Jaipur International Airport' },
  { code: 'COK', name: 'Kochi', fullName: 'COK, Cochin International Airport' },
  { code: 'TRV', name: 'Thiruvananthapuram', fullName: 'TRV, Trivandrum International Airport' },
  { code: 'CJB', name: 'Coimbatore', fullName: 'CJB, Coimbatore International Airport' },
  { code: 'NAG', name: 'Nagpur', fullName: 'NAG, Dr. Babasaheb Ambedkar International Airport' },
  { code: 'BBI', name: 'Bhubaneswar', fullName: 'BBI, Biju Patnaik International Airport' },
  { code: 'GAU', name: 'Guwahati', fullName: 'GAU, Lokpriya Gopinath Bordoloi International Airport' },
  { code: 'IDR', name: 'Indore', fullName: 'IDR, Devi Ahilya Bai Holkar Airport' },
  { code: 'BHO', name: 'Bhopal', fullName: 'BHO, Raja Bhoj Airport' },

  // US Airports - Chicago & New York
  { code: 'ORD', name: 'Chicago', fullName: 'ORD, Chicago O\'Hare International Airport' },
  { code: 'MDW', name: 'Chicago', fullName: 'MDW, Chicago Midway International Airport' },
  { code: 'JFK', name: 'New York', fullName: 'JFK, John F. Kennedy International Airport' },
  { code: 'LGA', name: 'New York', fullName: 'LGA, LaGuardia Airport' },
  { code: 'EWR', name: 'New York', fullName: 'EWR, Newark Liberty International Airport' },

  // Other Major US Airports
  { code: 'LAX', name: 'Los Angeles', fullName: 'LAX, Los Angeles International Airport' },
  { code: 'MIA', name: 'Miami', fullName: 'MIA, Miami International Airport' },
  { code: 'SFO', name: 'San Francisco', fullName: 'SFO, San Francisco International Airport' },
  { code: 'ATL', name: 'Atlanta', fullName: 'ATL, Hartsfield-Jackson Atlanta International Airport' },
  { code: 'DFW', name: 'Dallas', fullName: 'DFW, Dallas/Fort Worth International Airport' },

  // International Airports
  { code: 'DXB', name: 'Dubai', fullName: 'DXB, Dubai International Airport' },
  { code: 'SIN', name: 'Singapore', fullName: 'SIN, Singapore Changi Airport' },
  { code: 'LHR', name: 'London', fullName: 'LHR, London Heathrow Airport' }
];

export default function AirportSelector({
  selectedAirport,
  onAirportSelect,
  label,
  placeholder = "Search airports...",
  className = "",
  isMobile = false,
  useMobileBottomSheet = false
}) {
  const [showDropdown, setShowDropdown] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [airports, setAirports] = useState<AirportSelectorData[]>(fallbackAirports);
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch airports on component mount
  useEffect(() => {
    const fetchAirports = async () => {
      try {
        setLoading(true);
        setError(null);
        const airportData = await airportService.getAirports();
        setAirports(airportData);
        setInitialLoadComplete(true);
      } catch (err) {
        console.error('Failed to fetch airports:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to load airports';
        setError(`API Error: ${errorMessage}`);
        // Keep fallback airports on error
        setAirports(fallbackAirports);
        setInitialLoadComplete(true);
      } finally {
        setLoading(false);
      }
    };

    fetchAirports();
  }, []);

  // Search airports with debouncing and shimmer
  useEffect(() => {
    if (searchTerm.trim().length > 0) {
      // Show shimmer immediately when user starts typing
      setSearching(true);
      setError(null);

      // Clear previous timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Set new timeout for search
      searchTimeoutRef.current = setTimeout(async () => {
        try {
          const searchResults = await airportService.searchAirports(searchTerm);
          setAirports(searchResults);
        } catch (err) {
          console.error('Failed to search airports:', err);
          const errorMessage = err instanceof Error ? err.message : 'Search failed';
          setError(`Search Error: ${errorMessage}`);
          // Keep current airports on search error
        } finally {
          setSearching(false);
        }
      }, 300); // 300ms debounce
    }
    // Note: Removed the else if block that was causing duplicate API calls
    // The initial load is already handled by the first useEffect

    // Cleanup timeout on unmount
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchTerm]);

  // Click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [showDropdown]);

  const handleAirportClick = (airport: any) => {
    onAirportSelect(airport);
    setShowDropdown(false);
    setSearchTerm('');
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);

    // If search is cleared, reset to cached airports without API call
    if (value.trim().length === 0) {
      setSearching(false);
      // Reset to the airports that were loaded initially (from cache or API)
      // Don't make another API call here
    }
  };

  // Shimmer loading component
  const ShimmerItem = () => (
    <div className="w-full p-4 animate-pulse">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="ml-3">
          <div className="w-5 h-5 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );

  const filteredAirports = airports.filter(airport =>
    airport.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    airport.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    airport.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Mobile Bottom Sheet Modal
  if (useMobileBottomSheet && showDropdown) {
    return (
      <>
        {/* Trigger Button */}
        <div className={`relative ${className}`}>
          {label && (
            <label className="block text-sm font-medium text-gray-600">{label}</label>
          )}

          <button
            ref={buttonRef}
            onClick={() => setShowDropdown(!showDropdown)}
            className="w-full text-left"
          >
            <div className="font-bold text-lg text-gray-900">{selectedAirport.name}</div>
            <div className="text-xs text-gray-500 truncate">{selectedAirport.fullName}</div>
          </button>
        </div>

        {/* Bottom Sheet Modal */}
        <div className="fixed inset-0 bg-black/50 z-[70] flex items-end justify-center">
          <div className="bg-white rounded-t-2xl shadow-xl w-full max-h-[80vh] overflow-hidden animate-slide-up">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-800">Select Airport</h3>
              <button
                onClick={() => setShowDropdown(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-all"
              >
                <i className="ri-close-line text-xl text-gray-600"></i>
              </button>
            </div>

            {/* Search */}
            <div className="p-4 border-b border-gray-100">
              <div className="relative">
                <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <input
                  type="text"
                  placeholder={placeholder}
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg text-sm focus:outline-none focus:border-[#013688] bg-gray-50"
                  autoFocus
                />
              </div>
            </div>

            {/* Airport List */}
            <div className="overflow-y-auto max-h-[50vh]">
              <div className="p-4">
                <div className="text-xs font-semibold text-gray-500 mb-3 px-2">
                  {loading ? 'LOADING AIRPORTS...' : searching ? 'SEARCHING...' : 'AIRPORTS'}
                </div>
                <div className="space-y-1">
                  {(loading || searching) ? (
                    <>
                      {[...Array(5)].map((_, index) => (
                        <ShimmerItem key={index} />
                      ))}
                    </>
                  ) : error ? (
                    <div className="p-8 text-center text-gray-500">
                      <i className="ri-error-warning-line text-3xl text-red-300 mb-2"></i>
                      <div className="text-sm text-red-600">{error}</div>
                      <div className="text-xs text-gray-400 mt-1">Using fallback airports</div>
                    </div>
                  ) : (
                    <>
                      {filteredAirports.map((airport) => (
                        <button
                          key={airport.code}
                          onClick={() => handleAirportClick(airport)}
                          className="w-full p-4 hover:bg-gray-50 rounded-lg text-left transition-all border border-transparent hover:border-gray-200"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-semibold text-gray-900 text-base">{airport.name}</div>
                              <div className="text-sm text-gray-500 truncate mt-1">{airport.fullName}</div>
                            </div>
                            <div className="ml-3">
                              <i className="ri-plane-line text-gray-400 text-lg"></i>
                            </div>
                          </div>
                        </button>
                      ))}
                      {filteredAirports.length === 0 && !loading && (
                        <div className="p-8 text-center text-gray-500">
                          <i className="ri-search-line text-3xl text-gray-300 mb-2"></i>
                          <div className="text-sm">No airports found</div>
                          <div className="text-xs text-gray-400 mt-1">Try searching with different keywords</div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Regular Desktop/Mobile Dropdown
  return (
    <div className={`relative ${className} ${
      isMobile ? 'p-3 border border-gray-300 rounded-lg cursor-pointer' : ''
    }`}>
      {label && (
        <label className="block text-sm font-medium text-gray-600">{label}</label>
      )}

      <button
        ref={buttonRef}
        onClick={() => setShowDropdown(!showDropdown)}
        className={`w-full text-left `}
      >
        <div className={`font-bold ${isMobile ? 'text-lg' : 'text-xl'} text-gray-900`}>
          {selectedAirport.name}
        </div>
        <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 truncate`}>
          {selectedAirport.fullName}
        </div>
      </button>

      {/* Airport Dropdown */}
      {showDropdown && (
        <div
          ref={dropdownRef}
          className={`absolute ${
            isMobile
              ? 'top-full left-0 right-0'
              : 'top-5 w-[350px]'
          } bg-white border border-gray-200 rounded-lg shadow-xl z-50 max-h-80 overflow-y-auto mt-1`}
        >
          <div className="p-3 border-b border-gray-100">
            <input
              type="text"
              placeholder={placeholder}
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="w-full p-2 border border-gray-200 rounded text-sm focus:outline-none focus:border-[#013688]"
            />
          </div>
          <div className="p-2">
            <div className="text-xs font-semibold text-gray-500 mb-2 px-2">
              {loading ? 'LOADING AIRPORTS...' : searching ? 'SEARCHING...' : 'AIRPORTS'}
            </div>
            {(loading || searching) ? (
              <>
                {[...Array(4)].map((_, index) => (
                  <div key={index} className="p-3 animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </>
            ) : error ? (
              <div className="p-3 text-center text-gray-500">
                <div className="text-xs text-red-600">{error}</div>
                <div className="text-xs text-gray-400 mt-1">Using fallback airports</div>
              </div>
            ) : (
              <>
                {filteredAirports.map((airport) => (
                  <button
                    key={airport.code}
                    onClick={() => handleAirportClick(airport)}
                    className="w-full p-3 hover:bg-gray-50 rounded text-left transition-all"
                  >
                    <div className="font-semibold text-gray-900">{airport.name}</div>
                    <div className="text-sm text-gray-500 truncate">{airport.fullName}</div>
                  </button>
                ))}
                {filteredAirports.length === 0 && !loading && (
                  <div className="p-3 text-center text-gray-500 text-sm">
                    No airports found
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}