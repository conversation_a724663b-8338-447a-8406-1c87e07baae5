'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/app/components/ui/Header';
import Footer from '@/app/components/ui/Footer';
import { getAirlineLogo, getAirlineName, getDisplayFlightNumber } from '@/app/lib/airlineHelpers';

export default function BookingSuccessPage() {
  const [bookingData, setBookingData] = useState(null);

  // Mock booking data - normally would come from payment processing
  const mockBookingData = {
    pnr: 'MMT123456789',
    bookingId: 'BK789123456',
    paymentId: 'PAY987654321',
    totalAmount: 4698,
    paymentMethod: 'Credit Card ****1234',
    bookingDate: new Date().toLocaleDateString('en-IN', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }),
    flight: {
      airline: '6E',
      logo: '/AirlineLogo/6E.png',
      flightNumber: '6E-234',
      departure: { 
        time: '06:30', 
        airport: 'DEL', 
        city: 'Delhi', 
        terminal: 'Terminal 3',
        date: 'Friday, Aug 1' 
      },
      arrival: { 
        time: '09:15', 
        airport: 'BLR', 
        city: 'Bengaluru', 
        terminal: 'Terminal 2',
        date: 'Friday, Aug 1' 
      },
      duration: '2h 45m',
      stops: 'Non-stop',
      fareType: 'SAVER',
      baggage: { cabin: '7 kg', checkin: '15 kg' }
    },
    travelers: [
      {
        id: 1,
        title: 'Mr.',
        firstName: 'Rajesh',
        lastName: 'Kumar',
        gender: 'male',
        seatNumber: '12A'
      }
    ],
    contactDetails: {
      email: '<EMAIL>',
      phone: '+91 9876543210'
    },
    addons: {
      insurance: { selected: true, amount: 448 },
      extraBaggage: { selected: false, amount: 0 },
      meals: { selected: false, amount: 0 },
      priorityCheckin: { selected: false, amount: 0 }
    }
  };

  useEffect(() => {
    setBookingData(mockBookingData);
  }, []);

  const downloadTicket = () => {
    // Handle ticket download
    console.log('Downloading e-ticket...');
  };

  const viewItinerary = () => {
    // Handle itinerary view
    window.location.href = '/flights/itinerary';
  };

  const shareBooking = () => {
    // Handle booking share
    if (navigator.share) {
      navigator.share({
        title: 'Flight Booking Confirmation',
        text: `Flight booked successfully! PNR: ${bookingData.pnr}`,
        url: window.location.href,
      });
    }
  };

  if (!bookingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-[#013688] border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading booking confirmation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="pt-20 pb-16 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Success Banner */}
          <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-8 mb-8 text-white relative overflow-hidden">
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -ml-12 -mb-12"></div>
            
            <div className="relative z-10 text-center">
              <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="ri-check-line text-4xl text-green-500"></i>
              </div>
              <h1 className="text-3xl font-bold mb-2">Payment Completed Successfully!</h1>
              <p className="text-xl text-green-100 mb-4">Your flight has been booked</p>
              <div className="bg-white/20 rounded-lg p-4 inline-block">
                <p className="text-sm font-medium">Booking Reference</p>
                <p className="text-2xl font-bold">{bookingData.pnr}</p>
              </div>
            </div>
          </div>

          {/* Booking Details Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Flight Details */}
            <div className="space-y-6">
              {/* Flight Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-900">Flight Details</h2>
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-bold">CONFIRMED</span>
                </div>

                {/* Flight Route */}
                <div className="mb-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <img src={getAirlineLogo(bookingData.flight.airline, bookingData.flight.logo)} alt={getAirlineName(bookingData.flight.airline)} className="w-12 h-8 object-contain" />
                    <div>
                      <div className="font-semibold text-gray-900">{getAirlineName(bookingData.flight.airline)}</div>
                      <div className="text-sm text-gray-500">{getDisplayFlightNumber(bookingData.flight.airline, bookingData.flight.flightNumber)} • Economy • {bookingData.flight.fareType}</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900">{bookingData.flight.departure.time}</div>
                      <div className="text-sm text-gray-600">{bookingData.flight.departure.city}</div>
                      <div className="text-xs text-gray-500">{bookingData.flight.departure.airport}</div>
                      <div className="text-xs text-gray-500">{bookingData.flight.departure.terminal}</div>
                    </div>
                    
                    <div className="flex-1 px-4">
                      <div className="relative">
                        <div className="h-px bg-gray-300 w-full"></div>
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-2">
                          <i className="ri-plane-line text-[#013688]"></i>
                        </div>
                      </div>
                      <div className="text-center mt-1">
                        <div className="text-sm text-gray-600">{bookingData.flight.duration}</div>
                        <div className="text-xs text-green-600">{bookingData.flight.stops}</div>
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900">{bookingData.flight.arrival.time}</div>
                      <div className="text-sm text-gray-600">{bookingData.flight.arrival.city}</div>
                      <div className="text-xs text-gray-500">{bookingData.flight.arrival.airport}</div>
                      <div className="text-xs text-gray-500">{bookingData.flight.arrival.terminal}</div>
                    </div>
                  </div>

                  <div className="text-center text-sm text-gray-600 mb-4">
                    {bookingData.flight.departure.date}
                  </div>

                  {/* Baggage Info */}
                  <div className="flex items-center justify-center space-x-6 text-sm bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <i className="ri-suitcase-line text-gray-600"></i>
                      <span>Cabin: {bookingData.flight.baggage.cabin}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <i className="ri-luggage-cart-line text-gray-600"></i>
                      <span>Check-in: {bookingData.flight.baggage.checkin}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Traveler Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Traveler Details</h2>
                
                {bookingData.travelers.map((traveler, index) => (
                  <div key={traveler.id} className="border border-gray-200 rounded-lg p-4 mb-4 last:mb-0">
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-semibold text-gray-900">Passenger {index + 1}</span>
                      <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">Seat {traveler.seatNumber}</span>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Name:</span>
                        <div className="font-medium">{traveler.title} {traveler.firstName} {traveler.lastName}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Gender:</span>
                        <div className="font-medium capitalize">{traveler.gender}</div>
                      </div>
                    </div>
                  </div>
                ))}
                
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Contact Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Email:</span>
                      <div className="font-medium">{bookingData.contactDetails.email}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Phone:</span>
                      <div className="font-medium">{bookingData.contactDetails.phone}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Important Information */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <i className="ri-information-line text-blue-600 text-xl mt-1"></i>
                  <div>
                    <h3 className="font-semibold text-blue-900 mb-2">Important Information</h3>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Please arrive at the airport at least 2 hours before domestic flights</li>
                      <li>• Carry a valid government-issued photo ID for check-in</li>
                      <li>• Check-in closes 45 minutes before departure</li>
                      <li>• Your e-ticket has been sent to {bookingData.contactDetails.email}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Booking Summary & Actions */}
            <div className="space-y-6">
              {/* PNR & Booking Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Booking Summary</h2>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">PNR Number</span>
                    <span className="font-bold text-[#013688]">{bookingData.pnr}</span>
                  </div>
                  
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Booking ID</span>
                    <span className="font-medium">{bookingData.bookingId}</span>
                  </div>
                  
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Payment ID</span>
                    <span className="font-medium">{bookingData.paymentId}</span>
                  </div>
                  
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Booking Date</span>
                    <span className="font-medium">{bookingData.bookingDate}</span>
                  </div>
                  
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Payment Method</span>
                    <span className="font-medium">{bookingData.paymentMethod}</span>
                  </div>
                  
                  <div className="flex items-center justify-between py-3 bg-gray-50 rounded-lg px-4">
                    <span className="font-semibold text-gray-900">Total Amount Paid</span>
                    <span className="font-bold text-xl text-[#013688]">₹{bookingData.totalAmount.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Quick Actions</h2>
                
                <div className="space-y-3">
                  <button
                    onClick={viewItinerary}
                    className="w-full bg-[#013688] text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
                  >
                    <i className="ri-calendar-line"></i>
                    <span>VIEW ITINERARY</span>
                  </button>
                  
                  <button
                    onClick={downloadTicket}
                    className="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
                  >
                    <i className="ri-download-line"></i>
                    <span>DOWNLOAD E-TICKET</span>
                  </button>
                  
                  <button
                    onClick={shareBooking}
                    className="w-full bg-orange-500 text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors flex items-center justify-center space-x-2"
                  >
                    <i className="ri-share-line"></i>
                    <span>SHARE BOOKING</span>
                  </button>
                  
                  <Link href="/flights">
                    <button className="w-full bg-gray-100 text-gray-800 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2">
                      <i className="ri-search-line"></i>
                      <span>BOOK ANOTHER FLIGHT</span>
                    </button>
                  </Link>
                </div>
              </div>

              {/* Add-ons Summary */}
              {(bookingData.addons.insurance.selected || bookingData.addons.extraBaggage.selected) && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4">Add-ons Purchased</h2>
                  
                  <div className="space-y-3">
                    {bookingData.addons.insurance.selected && (
                      <div className="flex items-center justify-between py-2 border-b border-gray-100">
                        <div className="flex items-center space-x-2">
                          <i className="ri-shield-check-line text-green-600"></i>
                          <span className="text-sm">Travel Insurance</span>
                        </div>
                        <span className="font-semibold">₹{bookingData.addons.insurance.amount}</span>
                      </div>
                    )}
                    
                    {bookingData.addons.extraBaggage.selected && (
                      <div className="flex items-center justify-between py-2 border-b border-gray-100">
                        <div className="flex items-center space-x-2">
                          <i className="ri-luggage-cart-line text-blue-600"></i>
                          <span className="text-sm">Extra Baggage</span>
                        </div>
                        <span className="font-semibold">₹{bookingData.addons.extraBaggage.amount}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Customer Support */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <i className="ri-customer-service-line text-blue-600 text-2xl mt-1"></i>
                  <div>
                    <h3 className="font-semibold text-blue-900 mb-2">Need Help?</h3>
                    <p className="text-sm text-blue-800 mb-3">
                      Our customer support team is available 24/7 to assist you with your travel needs.
                    </p>
                    <div className="flex items-center space-x-4 text-sm">
                      <a href="tel:+918069012345" className="flex items-center space-x-1 text-blue-700 hover:text-blue-900">
                        <i className="ri-phone-line"></i>
                        <span>+91 80690 12345</span>
                      </a>
                      <a href="mailto:<EMAIL>" className="flex items-center space-x-1 text-blue-700 hover:text-blue-900">
                        <i className="ri-mail-line"></i>
                        <span>Support Email</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
}
