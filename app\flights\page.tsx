
'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import FlightSearch from '@/components/FlightSearch';
import FlightCard from './FlightCard';
import FlightFilters from './FlightFilters';
import FlightSort from './FlightSort';
import { useRouter } from 'next/navigation';
import { flightService, FlightSearchFormData, Flight, transformSearchListResponseToFlights, loadRoundTripData, isInternationalRoute } from '@/app/api/services/flightService';
import { getDisplayFlightNumber } from '../utils/airlineUtils';


import { airportService, AirportSelectorData } from '@/app/api/services/airportService';

function FlightSearchResults() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [onwardFlights, setOnwardFlights] = useState<Flight[]>([]);
  const [returnFlights, setReturnFlights] = useState<Flight[]>([]);
  const [filteredOnwardFlights, setFilteredOnwardFlights] = useState<Flight[]>([]);
  const [filteredReturnFlights, setFilteredReturnFlights] = useState<Flight[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dataSource, setDataSource] = useState<string>('api'); // Track data source for debugging
  const [sortBy, setSortBy] = useState('price');
  const [activeTab, setActiveTab] = useState('onward');
  const [viewMode, setViewMode] = useState('campaign'); // Will be updated based on route
  const [selectedOnward, setSelectedOnward] = useState<Flight | null>(null);
  const [selectedReturn, setSelectedReturn] = useState<Flight | null>(null);
  const [showModifyPopup, setShowModifyPopup] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [filters, setFilters] = useState({
    priceRange: [2000, 25000], // Increased upper limit to accommodate dummy data prices
    airlines: [],
    departure: [],
    arrival: [],
    stops: []
  });

  // Parse search parameters
  const from = searchParams.get('from') || 'DEL'; // Airport code
  const to = searchParams.get('to') || 'BLR'; // Airport code
  const fromName = searchParams.get('fromName') || 'Delhi'; // Display name
  const toName = searchParams.get('toName') || 'Bengaluru'; // Display name
  const departDate = searchParams.get('departDate') || searchParams.get('depart') || '31 Jul\'25';
  const returnDate = searchParams.get('return');
  const travelers = searchParams.get('travelers') || '1';
  const travelClass = searchParams.get('travelClass') || searchParams.get('class') || 'economy';
  const tripType = (searchParams.get('tripType') || 'oneWay').replace('-', ''); // Handle both 'one-way' and 'oneWay'
  const fareType = searchParams.get('fareType') || 'ON';
  const isRoundTrip = tripType === 'roundTrip' && returnDate;

  // Set view mode based on route type
  useEffect(() => {
    // Determine view mode based on route type
    // International flights: Campaign View, Domestic flights: Split View
    const isInternational = isInternationalRoute(from, to);
    setViewMode(isInternational ? 'campaign' : 'split');
  }, [from, to]);

  // Debug: Log all search parameters
  console.log('🔍 Flight search parameters:', {
    from, to, fromName, toName, departDate, returnDate,
    travelers, travelClass, tripType, fareType, isRoundTrip
  });

  // Helper function to transform mock data to Flight interface
  const transformMockToFlight = (mockFlight: any): Flight => ({
    id: mockFlight.id.toString(),
    airline: mockFlight.airline,
    logo: mockFlight.logo,
    flightNumber: mockFlight.flightNumber,
    segments: [{
      airline: mockFlight.airline,
      flightNumber: mockFlight.flightNumber,
      departure: {
        time: mockFlight.departure.time,
        airport: mockFlight.departure.airport,
        city: mockFlight.departure.city,
        date: new Date().toLocaleDateString()
      },
      arrival: {
        time: mockFlight.arrival.time,
        airport: mockFlight.arrival.airport,
        city: mockFlight.arrival.city,
        date: new Date().toLocaleDateString()
      },
      duration: mockFlight.duration,
      stops: mockFlight.stops
    }],
    departure: {
      time: mockFlight.departure.time,
      airport: mockFlight.departure.airport,
      city: mockFlight.departure.city,
      date: new Date().toLocaleDateString()
    },
    arrival: {
      time: mockFlight.arrival.time,
      airport: mockFlight.arrival.airport,
      city: mockFlight.arrival.city,
      date: new Date().toLocaleDateString()
    },
    duration: mockFlight.duration,
    stops: mockFlight.stops,
    price: mockFlight.price,
    originalPrice: mockFlight.originalPrice,
    currency: 'INR',
    baggage: {
      cabin: '7 kg',
      checkin: mockFlight.baggage || '15 kg'
    },
    cancellation: mockFlight.cancellation,
    rating: mockFlight.rating,
    features: mockFlight.features,
    fares: [{
      fareType: 'SAVER',
      price: mockFlight.price,
      originalPrice: mockFlight.originalPrice,
      currency: 'INR',
      baggage: {
        cabin: '7 kg',
        checkin: mockFlight.baggage || '15 kg'
      },
      cancellation: mockFlight.cancellation
    }],
    isRefundable: false,
    fareType: 'SAVER'
  });

  const mockOnwardFlights = [
    {
      id: 1,
      airline: '6E',
      logo: '/AirlineLogo/6E.png',
      flightNumber: '6E-234',
      departure: { time: '06:30', airport: 'DEL', city: 'Delhi' },
      arrival: { time: '09:15', airport: 'BLR', city: 'Bengaluru' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 4250,
      originalPrice: 5500,
      baggage: '15 kg',
      cancellation: 'Free cancellation till 2 hours before departure',
      rating: 4.2,
      features: ['On-time', 'Meals', 'Wi-Fi']
    },
    {
      id: 2,
      airline: 'SG',
      logo: '/AirlineLogo/SG.png',
      flightNumber: 'SG-126',
      departure: { time: '08:45', airport: 'DEL', city: 'Delhi' },
      arrival: { time: '11:30', airport: 'BLR', city: 'Bengaluru' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 3890,
      originalPrice: 4900,
      baggage: '15 kg',
      cancellation: 'Cancellation fee applies',
      rating: 4.0,
      features: ['On-time', 'Meals']
    },
    {
      id: 3,
      airline: 'AI',
      logo: '/AirlineLogo/AI.png',
      flightNumber: 'AI-512',
      departure: { time: '12:20', airport: 'DEL', city: 'Delhi' },
      arrival: { time: '15:05', airport: 'BLR', city: 'Bengaluru' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 5200,
      originalPrice: 6800,
      baggage: '20 kg',
      cancellation: 'Free cancellation till 4 hours before departure',
      rating: 4.1,
      features: ['Premium meals', 'Extra legroom', 'Priority boarding']
    },
    {
      id: 4,
      airline: 'UK',
      logo: '/AirlineLogo/UK.png',
      flightNumber: 'UK-864',
      departure: { time: '14:10', airport: 'DEL', city: 'Delhi' },
      arrival: { time: '16:55', airport: 'BLR', city: 'Bengaluru' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 6150,
      originalPrice: 7900,
      baggage: '20 kg',
      cancellation: 'Free cancellation till 6 hours before departure',
      rating: 4.5,
      features: ['Premium economy', 'Gourmet meals', 'Entertainment', 'Wi-Fi']
    },
    {
      id: 5,
      airline: 'G8',
      logo: '/AirlineLogo/G8.png',
      flightNumber: 'G8-345',
      departure: { time: '16:30', airport: 'DEL', city: 'Delhi' },
      arrival: { time: '19:15', airport: 'BLR', city: 'Bengaluru' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 3650,
      originalPrice: 4200,
      baggage: '15 kg',
      cancellation: 'Cancellation fee applies',
      rating: 3.9,
      features: ['On-time']
    },
    {
      id: 6,
      airline: '6E',
      logo: '/AirlineLogo/6E.png',
      flightNumber: '6E-578',
      departure: { time: '18:45', airport: 'DEL', city: 'Delhi' },
      arrival: { time: '21:30', airport: 'BLR', city: 'Bengaluru' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 4890,
      originalPrice: 6200,
      baggage: '15 kg',
      cancellation: 'Free cancellation till 2 hours before departure',
      rating: 4.3,
      features: ['On-time', 'Meals', 'Wi-Fi']
    }
  ];

  const mockReturnFlights = [
    {
      id: 11,
      airline: '6E',
      logo: '/AirlineLogo/6E.png',
      flightNumber: '6E-235',
      departure: { time: '07:20', airport: 'BLR', city: 'Bengaluru' },
      arrival: { time: '10:05', airport: 'DEL', city: 'Delhi' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 4580,
      originalPrice: 5800,
      baggage: '15 kg',
      cancellation: 'Free cancellation till 2 hours before departure',
      rating: 4.2,
      features: ['On-time', 'Meals', 'Wi-Fi']
    },
    {
      id: 12,
      airline: 'SG',
      logo: '/AirlineLogo/SG.png',
      flightNumber: 'SG-127',
      departure: { time: '09:35', airport: 'BLR', city: 'Bengaluru' },
      arrival: { time: '12:20', airport: 'DEL', city: 'Delhi' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 4120,
      originalPrice: 5200,
      baggage: '15 kg',
      cancellation: 'Cancellation fee applies',
      rating: 4.0,
      features: ['On-time', 'Meals']
    },
    {
      id: 13,
      airline: 'UK',
      logo: '/AirlineLogo/UK.png',
      flightNumber: 'UK-865',
      departure: { time: '15:25', airport: 'BLR', city: 'Bengaluru' },
      arrival: { time: '18:10', airport: 'DEL', city: 'Delhi' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 6480,
      originalPrice: 8200,
      baggage: '20 kg',
      cancellation: 'Free cancellation till 6 hours before departure',
      rating: 4.5,
      features: ['Premium economy', 'Gourmet meals', 'Entertainment', 'Wi-Fi']
    },
    {
      id: 14,
      airline: 'Air India',
      logo: '/AirlineLogo/AI.png',
      flightNumber: 'AI-513',
      departure: { time: '17:40', airport: 'BLR', city: 'Bengaluru' },
      arrival: { time: '20:25', airport: 'DEL', city: 'Delhi' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 5450,
      originalPrice: 7100,
      baggage: '20 kg',
      cancellation: 'Free cancellation till 4 hours before departure',
      rating: 4.1,
      features: ['Premium meals', 'Extra legroom', 'Priority boarding']
    },
    {
      id: 15,
      airline: 'GoFirst',
      logo: '/AirlineLogo/G8.png',
      flightNumber: 'G8-346',
      departure: { time: '19:50', airport: 'BLR', city: 'Bengaluru' },
      arrival: { time: '22:35', airport: 'DEL', city: 'Delhi' },
      duration: '2h 45m',
      stops: 'Non-stop',
      price: 3890,
      originalPrice: 4500,
      baggage: '15 kg',
      cancellation: 'Cancellation fee applies',
      rating: 3.9,
      features: ['On-time']
    }
  ];

  // Function to parse date from search params format
  const parseDateFromSearchParams = (dateStr: string): string => {
    try {
      console.log('📅 Parsing date:', dateStr);

      // Handle ISO format like "2024-01-15" (already in correct format)
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        console.log('✅ Date already in ISO format:', dateStr);
        return dateStr;
      }

      // Handle format like "31 Jul'25" or "01 Aug'25"
      const parts = dateStr.split(' ');
      if (parts.length === 2) {
        const day = parts[0].padStart(2, '0');
        const monthYear = parts[1];
        const month = monthYear.substring(0, 3);
        const year = monthYear.substring(4);

        const monthMap: { [key: string]: string } = {
          'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
          'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
          'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
        };

        const monthNum = monthMap[month] || '01';
        const fullYear = `20${year}`;
        const parsedDate = `${fullYear}-${monthNum}-${day}`;

        console.log('✅ Parsed date from custom format:', parsedDate);
        return parsedDate;
      }

      // Fallback to current date
      console.warn('⚠️ Could not parse date, using current date');
      return new Date().toISOString().split('T')[0];
    } catch (error) {
      console.error('❌ Error parsing date:', error);
      return new Date().toISOString().split('T')[0];
    }
  };

  // Function to get cabin class code
  const getCabinCode = (classStr: string): 'E' | 'PE' | 'B' | 'F' => {
    switch (classStr.toLowerCase()) {
      case 'business':
        return 'B';
      case 'first':
        return 'F';
      case 'premiumeconomy':
      case 'premium economy':
        return 'PE'; // Premium Economy
      default:
        return 'E'; // Economy
    }
  };

  // Load flight search results
  useEffect(() => {
    const searchFlights = async () => {
      if (!from || !to || !departDate) {
        setError('Missing required search parameters');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Get airport data for from and to using airport codes
        console.log('🛫 Looking up airports:', { from, to });
        console.log('🔍 Airport lookup process starting...');

        const fromAirport = await airportService.getAirportByCode(from);
        console.log('✈️ From airport lookup result:', { code: from, result: fromAirport });

        const toAirport = await airportService.getAirportByCode(to);
        console.log('🛬 To airport lookup result:', { code: to, result: toAirport });

        console.log('📊 Final airport lookup results:', {
          from: from,
          to: to,
          fromAirport: fromAirport,
          toAirport: toAirport,
          fromFound: !!fromAirport,
          toFound: !!toAirport
        });

        if (!fromAirport || !toAirport) {
          console.error('❌ Airport lookup failed - this will prevent search API call:', {
            from,
            to,
            fromAirport,
            toAirport,
            fromMissing: !fromAirport,
            toMissing: !toAirport
          });
          throw new Error(`Airport not found: ${!fromAirport ? from : ''} ${!toAirport ? to : ''}`);
        }

        console.log('✅ Airport lookup successful, proceeding with search API call');

        // Parse travelers count
        const totalTravelers = parseInt(travelers) || 1;

        // Prepare search form data
        const searchFormData: FlightSearchFormData = {
          from: fromAirport,
          to: toAirport,
          departureDate: parseDateFromSearchParams(departDate),
          returnDate: returnDate ? parseDateFromSearchParams(returnDate) : undefined,
          adults: totalTravelers, // For now, assume all are adults
          children: 0,
          infants: 0,
          cabin: getCabinCode(travelClass),
          tripType: tripType as 'oneWay' | 'roundTrip',
          fareType: fareType as 'ON' | 'OFF',
          isDirect: false // Can be made configurable
        };

        console.log('🚀 Searching flights with data:', searchFormData);

        // Search flights using the service with search-list integration
        const { searchResponse, searchListResponse } = await flightService.searchFlightsWithFormDataAndSearchList(searchFormData);

        console.log('📡 Flight service response received:', {
          success: searchResponse.success,
          message: searchResponse.message,
          onwardFlightsCount: searchResponse.data?.onwardFlights?.length || 0,
          returnFlightsCount: searchResponse.data?.returnFlights?.length || 0,
          totalResults: searchResponse.data?.totalResults,
          hasTUI: !!searchResponse.data?.tui,
          hasSearchListResponse: !!searchListResponse
        });

        if (searchResponse.success && searchResponse.data) {
          let onwardFlights = searchResponse.data.onwardFlights;
          let returnFlights = searchResponse.data.returnFlights;

          // If we have search-list response, use those flights instead
          if (searchListResponse && searchListResponse.Trips && searchListResponse.Trips.length > 0) {
            console.log('✅ Using search-list response for flight data');
            console.log('SearchListResponse structure:', {
              completed: searchListResponse.Completed,
              tripsCount: searchListResponse.Trips.length,
              firstTripJourneyCount: searchListResponse.Trips[0]?.Journey?.length || 0
            });

            const searchListFlights = transformSearchListResponseToFlights(searchListResponse);
            console.log('🔄 Transformed flights from search-list:', {
              count: searchListFlights.length,
              firstFlight: searchListFlights[0] ? {
                id: searchListFlights[0].id,
                airline: searchListFlights[0].airline,
                flightNumber: searchListFlights[0].flightNumber,
                price: searchListFlights[0].price,
                departure: searchListFlights[0].departure,
                arrival: searchListFlights[0].arrival
              } : null
            });

            onwardFlights = searchListFlights;

            // For round trip, we might need to separate onward and return flights
            // This depends on how the API returns round trip data
            if (isRoundTrip) {
              // For now, use the same flights for return - this might need adjustment based on API behavior
              returnFlights = searchListFlights;
            }
          } else {
            console.log('ℹ️ No search-list response or no trips found, using original search results');
          }

          console.log('Setting flights state:', {
            onwardFlightsCount: onwardFlights.length,
            returnFlightsCount: returnFlights?.length || 0,
            isRoundTrip,
            sampleOnwardFlight: onwardFlights[0] ? {
              id: onwardFlights[0].id,
              price: onwardFlights[0].price,
              airline: onwardFlights[0].airline
            } : null
          });

          setOnwardFlights(onwardFlights);
          setFilteredOnwardFlights(onwardFlights);

          if (isRoundTrip && returnFlights) {
            setReturnFlights(returnFlights);
            setFilteredReturnFlights(returnFlights);
          }

          // Update price range filter to accommodate the actual flight prices
          if (onwardFlights.length > 0) {
            const allFlights = isRoundTrip && returnFlights ? [...onwardFlights, ...returnFlights] : onwardFlights;
            const minPrice = Math.min(...allFlights.map(f => f.price));
            const maxPrice = Math.max(...allFlights.map(f => f.price));

            console.log('Updating price range filter:', {
              minPrice,
              maxPrice,
              currentRange: filters.priceRange
            });

            // Only update if the current range doesn't accommodate all flights
            if (minPrice < filters.priceRange[0] || maxPrice > filters.priceRange[1]) {
              setFilters(prev => ({
                ...prev,
                priceRange: [Math.min(minPrice, prev.priceRange[0]), Math.max(maxPrice, prev.priceRange[1])]
              }));
            }
          }

          // Log the data source for debugging
          console.log('Flight data source:', {
            flightCount: onwardFlights.length,
            hasTUI: !!searchResponse.data.tui,
            searchMessage: searchResponse.message
          });
        } else {
          throw new Error(searchResponse.message || 'Failed to search flights');
        }

      } catch (error) {
        console.error('Error searching flights:', error);
        setError(error instanceof Error ? error.message : 'Failed to search flights');

        // Fallback to JSON data for development
        console.log('Falling back to JSON data...');

        if (isRoundTrip) {
          try {
            // Load round trip data from JSON files
            const roundTripData = await loadRoundTripData(from, to);

            if (roundTripData.success && roundTripData.data) {
              // Transform onward flights
              const transformedOnwardFlights = roundTripData.data.onwardFlights || [];
              setOnwardFlights(transformedOnwardFlights);
              setFilteredOnwardFlights(transformedOnwardFlights);

              // Transform return flights
              const transformedReturnFlights = roundTripData.data.returnFlights || [];
              setReturnFlights(transformedReturnFlights);
              setFilteredReturnFlights(transformedReturnFlights);

              console.log(`✅ Loaded ${transformedOnwardFlights.length} onward and ${transformedReturnFlights.length} return flights from JSON`);
              setDataSource(`json-${roundTripData.data.searchInfo?.flightType || 'unknown'}`);
            } else {
              throw new Error('Failed to load round trip JSON data');
            }
          } catch (jsonError) {
            console.error('Error loading JSON data, falling back to hardcoded mock data:', jsonError);
            // Final fallback to hardcoded mock data
            const transformedOnwardFlights = mockOnwardFlights.map(transformMockToFlight);
            setOnwardFlights(transformedOnwardFlights);
            setFilteredOnwardFlights(transformedOnwardFlights);

            const transformedReturnFlights = mockReturnFlights.map(transformMockToFlight);
            setReturnFlights(transformedReturnFlights);
            setFilteredReturnFlights(transformedReturnFlights);
            setDataSource('mock-hardcoded');
          }
        } else {
          // For one-way trips, use existing mock data
          const transformedOnwardFlights = mockOnwardFlights.map(transformMockToFlight);
          setOnwardFlights(transformedOnwardFlights);
          setFilteredOnwardFlights(transformedOnwardFlights);
          setDataSource('mock-oneway');
        }
      } finally {
        setLoading(false);
      }
    };

    searchFlights();
  }, [from, to, departDate, returnDate, travelers, travelClass, tripType, fareType, isRoundTrip]);

  const applyFiltersAndSort = (flights, filtersToApply, sortByOption) => {
    let filtered = [...flights];

    if (filtersToApply.priceRange) {
      filtered = filtered.filter(flight => 
        flight.price >= filtersToApply.priceRange[0] && flight.price <= filtersToApply.priceRange[1]
      );
    }

    if (filtersToApply.airlines.length > 0) {
      filtered = filtered.filter(flight => filtersToApply.airlines.includes(flight.airline));
    }

    if (filtersToApply.departure.length > 0) {
      filtered = filtered.filter(flight => {
        const hour = parseInt(flight.departure.time.split(':')[0]);
        return filtersToApply.departure.some(slot => {
          if (slot === 'morning' && hour >= 6 && hour < 12) return true;
          if (slot === 'afternoon' && hour >= 12 && hour < 18) return true;
          if (slot === 'evening' && hour >= 18 && hour < 24) return true;
          if (slot === 'night' && (hour >= 0 && hour < 6)) return true;
          return false;
        });
      });
    }

    switch (sortByOption) {
      case 'price':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'duration':
        filtered.sort((a, b) => a.duration.localeCompare(b.duration));
        break;
      case 'departure':
        filtered.sort((a, b) => a.departure.time.localeCompare(b.departure.time));
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      default:
        break;
    }

    return filtered;
  };

  const handleBookNow = (flight) => {
    // Save complete flight details to localStorage for booking preview
    const flightBookingData = {
      flight: flight,
      bookingDetails: {
        fareType: 'SAVER',
        bookingDate: new Date().toISOString(),
        searchParams: {
          from: from,
          to: to,
          departDate: departDate,
          returnDate: returnDate,
          travelers: travelers,
          travelClass: travelClass,
          tripType: tripType
        }
      }
    };

    localStorage.setItem('selectedFlightForBooking', JSON.stringify(flightBookingData));
    console.log('Flight saved to localStorage for booking:', flightBookingData);

    const bookingParams = new URLSearchParams({
      flightId: flight.id.toString(),
      fareType: 'saver',
      price: flight.price.toString(),
      from: flight.departure.city,
      to: flight.arrival.city
    });

    router.push(`/flights/booking?${bookingParams.toString()}`);
  };

  useEffect(() => {
    console.log('Applying filters and sort:', {
      onwardFlightsCount: onwardFlights.length,
      filters,
      sortBy,
      priceRangeFilter: filters.priceRange,
      sampleFlightPrices: onwardFlights.slice(0, 3).map(f => f.price),
      sampleFlight: onwardFlights[0] ? {
        id: onwardFlights[0].id,
        price: onwardFlights[0].price,
        airline: onwardFlights[0].airline,
        departure: onwardFlights[0].departure
      } : null
    });

    const filteredOnward = applyFiltersAndSort(onwardFlights, filters, sortBy);
    console.log('Filtered onward flights:', {
      originalCount: onwardFlights.length,
      filteredCount: filteredOnward.length,
      sampleFiltered: filteredOnward[0] ? {
        id: filteredOnward[0].id,
        price: filteredOnward[0].price,
        airline: filteredOnward[0].airline
      } : null
    });

    setFilteredOnwardFlights(filteredOnward);

    if (isRoundTrip) {
      const filteredReturn = applyFiltersAndSort(returnFlights, filters, sortBy);
      setFilteredReturnFlights(filteredReturn);
    }
  }, [onwardFlights, returnFlights, filters, sortBy, isRoundTrip]);

  const getCurrentFlights = () => {
    if (!isRoundTrip) return filteredOnwardFlights;
    return activeTab === 'onward' ? filteredOnwardFlights : filteredReturnFlights;
  };

  const getAllFlightsForFilters = () => {
    return isRoundTrip ? [...onwardFlights, ...returnFlights] : onwardFlights;
  };

  const getTotalPrice = () => {
    if (!selectedOnward || !selectedReturn) return 0;
    return selectedOnward.price + selectedReturn.price;
  };

  const handleOnwardSelection = (flight) => {
    setSelectedOnward(flight);
  };

  const handleReturnSelection = (flight) => {
    setSelectedReturn(flight);
  };

  const handleModifySearch = () => {
    setShowModifyPopup(true);
  };

  const handleCloseModifyPopup = () => {
    setShowModifyPopup(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="pt-20 pb-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              </div>
              <div className="lg:col-span-3 space-y-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-white rounded-lg shadow-sm p-6">
                    <div className="animate-pulse">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-16 bg-gray-200 rounded"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                        </div>
                        <div className="h-8 w-24 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="pt-20 pb-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="mb-4">
                <i className="ri-error-warning-line text-4xl text-red-500"></i>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Flight Search Error</h2>
              <p className="text-gray-600 mb-6">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      <Header />

      <div className="pt-20 pb-32 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Search Summary */}
          <div className="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-6">
            <div className="flex items-center justify-between flex-wrap gap-4">
              <div>
                <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
                  {from} → {to} {isRoundTrip && '• Round Trip'}
                </h1>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span>Depart: {departDate}</span>
                  {returnDate && <span>• Return: {returnDate}</span>}
                  <span>• {travelers} Traveller{parseInt(travelers) > 1 ? 's' : ''}</span>
                  <span>• {travelClass.charAt(0).toUpperCase() + travelClass.slice(1)}</span>
                  {process.env.NODE_ENV === 'development' && (
                    <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                      Data: {dataSource}
                    </span>
                  )}
                </div>
              </div>
              <button 
                onClick={handleModifySearch}
                className="text-[#013688] font-semibold hover:text-blue-700 transition-colors"
              >
                Modify Search
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Filters */}
            <div className="lg:col-span-1">
              <FlightFilters
                filters={filters} 
                setFilters={setFilters}
                flights={getAllFlightsForFilters()}
              />
            </div>

            {/* Results */}
            <div className="lg:col-span-3">
              {/* Campaign View - Combined Flight Cards */}
              {viewMode === 'campaign' && isRoundTrip && (
                <>
                  {/* Sort Options */}
                  <FlightSort 
                    sortBy={sortBy} 
                    setSortBy={setSortBy} 
                    resultsCount={filteredOnwardFlights.length}
                    journeyType="Round Trip"
                  />

                  {/* Combined Flight Cards */}
                  <div className="space-y-4">
                    {filteredOnwardFlights.length > 0 && filteredReturnFlights.length > 0 ? (
                      filteredOnwardFlights.map((onwardFlight, index) => {
                        const returnFlight = filteredReturnFlights[index % filteredReturnFlights.length];
                        const totalPrice = onwardFlight.price + returnFlight.price;
                        const totalOriginalPrice = (onwardFlight.originalPrice || onwardFlight.price) + (returnFlight.originalPrice || returnFlight.price);
                        const savings = totalOriginalPrice - totalPrice;

                        return (
                          <div key={`${onwardFlight.id}-${returnFlight.id}`} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            {/* Combined Flight Header */}
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center space-x-4">
                                <div className="flex items-center space-x-2">
                                  <img src={onwardFlight.logo} alt={onwardFlight.airline} className="w-10 h-6 object-contain" />
                                  <span className="text-sm font-medium text-gray-700">{onwardFlight.airline}</span>
                                </div>
                                <div className="text-xs text-gray-500">+</div>
                                <div className="flex items-center space-x-2">
                                  <img src={returnFlight.logo} alt={returnFlight.airline} className="w-10 h-6 object-contain" />
                                  <span className="text-sm font-medium text-gray-700">{returnFlight.airline}</span>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <div className="flex items-center space-x-1 text-xs text-green-600">
                                  <i className="ri-leaf-line"></i>
                                  <span>ATFLY</span>
                                  <i className="ri-verified-badge-fill text-green-600"></i>
                                  <span className="font-medium">₹{savings}</span>
                                </div>
                              </div>
                            </div>

                            {/* Flight Details Grid */}
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                              {/* Onward Flight */}
                              <div className="space-y-3">
                                <div className="flex items-center space-x-2 text-sm text-gray-600">
                                  <i className="ri-calendar-line"></i>
                                  <span className="font-medium">Departure</span>
                                  <span>|</span>
                                  <span>{departDate}</span>
                                  <span>|</span>
                                  <span className="text-blue-600">{onwardFlight.airline}</span>
                                </div>
                                
                                <div className="flex items-center justify-between">
                                  <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{onwardFlight.departure.time}</div>
                                    <div className="text-sm text-gray-600">{onwardFlight.departure.city}</div>
                                    <div className="text-xs text-gray-500">{onwardFlight.departure.airport}</div>
                                  </div>
                                  
                                  <div className="flex-1 px-4">
                                    <div className="relative">
                                      <div className="h-px bg-gray-300 w-full"></div>
                                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-2">
                                        <i className="ri-plane-line text-blue-600"></i>
                                      </div>
                                    </div>
                                    <div className="text-center mt-1">
                                      <div className="text-sm font-medium text-gray-700">{onwardFlight.duration}</div>
                                      <div className="text-xs text-green-600">{onwardFlight.stops}</div>
                                    </div>
                                  </div>
                                  
                                  <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{onwardFlight.arrival.time}</div>
                                    <div className="text-sm text-gray-600">{onwardFlight.arrival.city}</div>
                                    <div className="text-xs text-gray-500">{onwardFlight.arrival.airport}</div>
                                  </div>
                                </div>
                              </div>

                              {/* Return Flight */}
                              <div className="space-y-3">
                                <div className="flex items-center space-x-2 text-sm text-gray-600">
                                  <i className="ri-calendar-line"></i>
                                  <span className="font-medium">Return</span>
                                  <span>|</span>
                                  <span>{returnDate}</span>
                                  <span>|</span>
                                  <span className="text-blue-600">{returnFlight.airline}</span>
                                </div>
                                
                                <div className="flex items-center justify-between">
                                  <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{returnFlight.departure.time}</div>
                                    <div className="text-sm text-gray-600">{returnFlight.departure.city}</div>
                                    <div className="text-xs text-gray-500">{returnFlight.departure.airport}</div>
                                  </div>
                                  
                                  <div className="flex-1 px-4">
                                    <div className="relative">
                                      <div className="h-px bg-gray-300 w-full"></div>
                                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-2">
                                        <i className="ri-plane-line text-blue-600" style={{transform: 'rotate(180deg)'}}></i>
                                      </div>
                                    </div>
                                    <div className="text-center mt-1">
                                      <div className="text-sm font-medium text-gray-700">{returnFlight.duration}</div>
                                      <div className="text-xs text-green-600">{returnFlight.stops}</div>
                                    </div>
                                  </div>
                                  
                                  <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{returnFlight.arrival.time}</div>
                                    <div className="text-sm text-gray-600">{returnFlight.arrival.city}</div>
                                    <div className="text-xs text-gray-500">{returnFlight.arrival.airport}</div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Flight Numbers and Features */}
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4 text-xs text-gray-600">
                              <div>
                                <span className="font-medium">{getDisplayFlightNumber(onwardFlight.airline, onwardFlight.flightNumber)}</span>
                                {onwardFlight.features.map((feature, idx) => (
                                  <span key={idx} className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 rounded">
                                    {feature}
                                  </span>
                                ))}
                              </div>
                              <div>
                                <span className="font-medium">{getDisplayFlightNumber(returnFlight.airline, returnFlight.flightNumber)}</span>
                                {returnFlight.features.map((feature, idx) => (
                                  <span key={idx} className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 rounded">
                                    {feature}
                                  </span>
                                ))}
                              </div>
                            </div>

                            {/* Pricing and Book Button */}
                            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                              <div className="flex items-center space-x-4 text-sm text-gray-600">
                                <div className="flex items-center space-x-2">
                                  <i className="ri-shield-check-line text-green-600"></i>
                                  <span>Refundable</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <i className="ri-suitcase-line"></i>
                                  <span>{onwardFlight.baggage?.checkin || '15 kg'} + {returnFlight.baggage?.checkin || '15 kg'}</span>
                                </div>
                              </div>
                              
                              <div className="flex items-center space-x-4">
                                <div className="text-right">
                                  <div className="flex items-center space-x-2">
                                    <span className="text-sm text-gray-500 line-through">₹{totalOriginalPrice.toLocaleString()}</span>
                                    <span className="text-2xl font-bold text-gray-900">₹{totalPrice.toLocaleString()}</span>
                                  </div>
                                  <div className="text-xs text-gray-600">+ Flight Details</div>
                                </div>
                                <button className="bg-[#ff6b35] text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-600 transition-colors whitespace-nowrap" onClick={() => handleBookNow(onwardFlight)}>
                                  Book Now
                                </button>
                              </div>
                            </div>
                          </div>
                        );
                      }) 
                    ) : (
                      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                        <div className="w-16 h-16 flex items-center justify-center mx-auto mb-4 bg-gray-100 rounded-full">
                          <i className="ri-plane-line text-2xl text-gray-400"></i>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No round trip combinations found</h3>
                        <p className="text-gray-600 mb-4">Try adjusting your filters or search criteria</p>
                        <button
                          onClick={() => {
                            setFilters({
                              priceRange: [2000, 25000],
                              airlines: [],
                              departure: [],
                              arrival: [],
                              stops: []
                            });
                          }}
                          className="bg-[#013688] text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                        >
                          Clear Filters
                        </button>
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Split View */}
              {viewMode === 'split' && isRoundTrip && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* Onward Flights */}
                  <div>
                    <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
                      <h3 className="font-semibold text-gray-900 mb-2">
                        <div className="flex items-center space-x-2">
                          <i className="ri-plane-line text-[#013688]"></i>
                          <span>Onward Journey</span>
                        </div>
                        <div className="text-sm font-normal text-gray-600">{from} → {to} • {departDate}</div>
                      </h3>
                    </div>
                    <div className="space-y-3">
                      {filteredOnwardFlights.map((flight) => (
                        <div 
                          key={flight.id}
                          className={`cursor-pointer transition-all ${
                            selectedOnward?.id === flight.id ? 'ring-2 ring-[#013688]' : ''
                          }`}
                          onClick={() => handleOnwardSelection(flight)}
                        >
                          <FlightCard 
                            flight={flight} 
                            journeyType="onward"
                            compact={true}
                            selected={selectedOnward?.id === flight.id}
                            tripType={tripType}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Return Flights */}
                  <div>
                    <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
                      <h3 className="font-semibold text-gray-900 mb-2">
                        <div className="flex items-center space-x-2">
                          <i className="ri-plane-line text-[#013688]" style={{transform: 'rotate(180deg)'}}></i>
                          <span>Return Journey</span>
                        </div>
                        <div className="text-sm font-normal text-gray-600">{to} → {from} • {returnDate}</div>
                      </h3>
                    </div>
                    <div className="space-y-3">
                      {filteredReturnFlights.map((flight) => (
                        <div 
                          key={flight.id}
                          className={`cursor-pointer transition-all ${
                            selectedReturn?.id === flight.id ? 'ring-2 ring-[#013688]' : ''
                          }`}
                          onClick={() => handleReturnSelection(flight)}
                        >
                          <FlightCard 
                            flight={flight} 
                            journeyType="return"
                            compact={true}
                            selected={selectedReturn?.id === flight.id}
                            tripType={tripType}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Single Trip View */}
              {!isRoundTrip && (
                <>
                  {/* Sort Options */}
                  <FlightSort 
                    sortBy={sortBy} 
                    setSortBy={setSortBy} 
                    resultsCount={getCurrentFlights().length}
                    journeyType="Onward"
                  />

                  {/* Flight Cards */}
                  <div className="space-y-4">
                    {getCurrentFlights().length > 0 ? (
                      getCurrentFlights().map((flight) => (
                        <FlightCard 
                          key={flight.id} 
                          flight={flight} 
                          journeyType="onward"
                          tripType={tripType}
                        />
                      ))
                    ) : (
                      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                        <div className="w-16 h-16 flex items-center justify-center mx-auto mb-4 bg-gray-100 rounded-full">
                          <i className="ri-plane-line text-2xl text-gray-400"></i>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No flights found</h3>
                        <p className="text-gray-600 mb-4">Try adjusting your filters or search criteria</p>
                        <button 
                          onClick={() => {
                            setFilters({
                              priceRange: [2000, 25000],
                              airlines: [],
                              departure: [],
                              arrival: [],
                              stops: []
                            });
                          }}
                          className="bg-[#013688] text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                        >
                          Clear Filters
                        </button>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Modify Search Popup */}
      {showModifyPopup && (
        <div className="fixed inset-0 bg-black/50 z-[100] flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            {/* Popup Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900">Modify Flight Search</h2>
              <button 
                onClick={handleCloseModifyPopup}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <i className="ri-close-line text-xl text-gray-500"></i>
              </button>
            </div>

            {/* Flight Search Component */}
            <div>
              <FlightSearch isModify={true} />
            </div>
          </div>
        </div>
      )}

      {/* Fixed Bottom Selection Bar for Split View */}
      {viewMode === 'split' && isRoundTrip && (selectedOnward || selectedReturn) && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
          <div className="max-w-7xl mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                {/* Onward Selection */}
                <div className="flex items-center space-x-3">
                  <div className="text-sm font-medium text-gray-600">ONWARD</div>
                  {selectedOnward ? (
                    <div className="flex items-center space-x-3 bg-green-50 border border-green-200 rounded-lg px-3 py-2">
                      <img src={selectedOnward.logo} alt={selectedOnward.airline} className="w-8 h-5 object-contain" />
                      <div className="text-sm">
                        <div className="font-medium text-gray-900">{selectedOnward.flightNumber}</div>
                        <div className="text-xs text-gray-600">{selectedOnward.departure.time} - {selectedOnward.arrival.time}</div>
                      </div>
                      <div className="text-sm font-bold text-gray-900">₹{selectedOnward.price.toLocaleString()}</div>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-400 px-3 py-2 border border-gray-200 rounded-lg bg-gray-50">
                      Select onward flight
                    </div>
                  )}
                </div>

                {/* Return Selection */}
                <div className="flex items-center space-x-3">
                  <div className="text-sm font-medium text-gray-600">RETURN</div>
                  {selectedReturn ? (
                    <div className="flex items-center space-x-3 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                      <img src={selectedReturn.logo} alt={selectedReturn.airline} className="w-8 h-5 object-contain" />
                      <div className="text-sm">
                        <div className="font-medium text-gray-900">{selectedReturn.flightNumber}</div>
                        <div className="text-xs text-gray-600">{selectedReturn.departure.time} - {selectedReturn.arrival.time}</div>
                      </div>
                      <div className="text-sm font-bold text-gray-900">₹{selectedReturn.price.toLocaleString()}</div>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-400 px-3 py-2 border border-gray-200 rounded-lg bg-gray-50">
                      Select return flight
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {selectedOnward && selectedReturn && (
                  <div className="text-right">
                    <div className="text-sm text-gray-600">Total Amount</div>
                    <div className="text-xl font-bold text-[#013688]">₹{getTotalPrice().toLocaleString()}</div>
                  </div>
                )}
                <button 
                  className={`px-6 py-3 rounded-lg font-semibold transition-all whitespace-nowrap ${
                    selectedOnward && selectedReturn
                      ? 'bg-[#ff6b35] text-white hover:bg-red-600'
                      : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  }`}
                  disabled={!selectedOnward || !selectedReturn}
                  onClick={() => handleBookNow(selectedOnward)}
                >
                  Book Flights
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
}

export default function FlightsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-[#013688] border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading flights...</p>
        </div>
      </div>
    }>
      <FlightSearchResults />
    </Suspense>
  );
}
