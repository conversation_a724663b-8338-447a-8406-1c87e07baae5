# Updated Preview Integration - API First with JSON Fallback

## ✅ What I've Updated

Based on your updated `previewdummy.json` file structure, I've completely updated the preview integration to:

1. **API-First Approach**: Try real API first, only use JSON when API fails
2. **Updated Data Structure**: Match your new comprehensive JSON format
3. **Proper Integration**: Works with booking preview page

## 🔧 Changes Made

### 1. Updated Data Types (`app/api/services/flightService.ts`)

**New Structure Matches Your JSON:**
```typescript
export interface PreviewData {
  TUI: string;
  Code: string;
  Msg: string[];
  From: string;
  To: string;
  FromName: string;
  ToName: string;
  OnwardDate: string;
  ReturnDate: string;
  NetAmount: number;
  GrossAmount: number;
  InsPremium: number;
  FareType: string;
  Trips: PreviewTrip[];
  Rules: any[];
  SSR: any[];
  INSFare: any[];
}
```

### 2. Updated Service Method

**API-First Approach:**
```typescript
async getPreviewData(tui?: string, from?: string, to?: string): Promise<PreviewResponse> {
  try {
    // Try API first
    const FORCE_PREVIEW_DUMMY_DATA_FOR_TESTING = false; // API first!
    
    // Call real API with TUI/flight details
    const apiResponse = await fetch(`/api/preview?tui=${tui}&from=${from}&to=${to}`);
    
    if (apiResponse.ok) {
      return await apiResponse.json(); // Return API data
    } else {
      throw new Error('API failed');
    }
  } catch (apiError) {
    // Only fallback to JSON when API fails
    console.warn('⚠️ API failed, using previewdummy.json');
    const dummyData = await loadDummyPreviewData();
    return {
      success: true,
      message: 'Preview data loaded from previewdummy.json file (API not available)',
      data: dummyData
    };
  }
}
```

### 3. Updated API Route (`app/api/preview/route.ts`)

**Returns New Format:**
```typescript
// Mock API returns data in your JSON structure
const mockApiData = {
  TUI: "API_MOCK_TUI_12345",
  Code: "200",
  Msg: ["Success from API"],
  From: from || "BOM",
  To: to || "DEL",
  NetAmount: 4200,
  GrossAmount: 4300,
  InsPremium: 199,
  FareType: "ON",
  Trips: [...], // Flight segments
  Rules: [],
  SSR: [],
  INSFare: []
};
```

### 4. Updated Booking Page (`app/flights/booking/page.tsx`)

**Integration with New Data:**
- ✅ Loads flight details from localStorage
- ✅ Calls preview API with flight details (TUI, from, to)
- ✅ Uses preview data for additional charges (insurance premium)
- ✅ Shows data source (API vs JSON file)
- ✅ Displays TUI and net amount in indicator

## 🎯 How It Works Now

### Flow:
1. **User selects flight** → Flight details saved to localStorage
2. **Navigate to booking** → Booking page loads
3. **Try API first** → Calls `/api/preview` with flight details
4. **API Success** → Uses API data (shows "API" in indicator)
5. **API Fails** → Falls back to your `previewdummy.json` (shows "previewdummy.json")

### Data Sources:
- **Primary**: Real API (when available)
- **Fallback**: Your `previewdummy.json` file (when API fails)
- **Flight Details**: Always from localStorage (selected flight)

### Visual Indicators:
- ✅ Shows data source (API or JSON file)
- ✅ Displays TUI from preview data
- ✅ Shows net amount from preview data
- ✅ Insurance premium added to final price

## 📊 Your New JSON Structure Usage

Your comprehensive `previewdummy.json` now includes:
- **Flight Details**: Complete segment information
- **Fare Breakdown**: Net/Gross amounts, taxes, commissions
- **Rules**: Cancellation and change policies
- **SSR**: Special service requests (baggage, meals)
- **Insurance**: Premium calculations
- **TUI**: Transaction unique identifier

## 🚀 Test the Integration

1. **API Mode** (Default):
   - Service tries API first
   - Falls back to JSON only if API fails
   - Shows "API" or "previewdummy.json" in data source indicator

2. **Force JSON Mode** (For Testing):
   ```typescript
   const FORCE_PREVIEW_DUMMY_DATA_FOR_TESTING = true; // Force JSON
   ```

3. **Check Data Source**:
   - Blue indicator shows where data came from
   - TUI and net amount displayed
   - Insurance premium added to booking total

## 🔄 API Integration Ready

When you have your real API:
1. Update the API endpoint URL in the service
2. Add authentication headers if needed
3. The fallback to JSON will work automatically when API is down

The system now properly tries API first and only uses your JSON file when the API doesn't respond! 🎉
