'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { flightService, PreviewData, PreviewResponse } from '@/app/api/services/flightService';

export default function PreviewPage() {
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch preview data
  const fetchPreviewData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🚀 Fetching preview data from previewdummy.json...');

      // Force loading from JSON file to show your actual data
      const response: PreviewResponse = await flightService.getPreviewData();

      console.log('📡 Preview service response received:', {
        success: response.success,
        message: response.message,
        hasData: !!response.data
      });

      if (response.success && response.data) {
        setPreviewData(response.data);
        console.log('✅ Preview data set successfully:', response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch preview data');
      }
    } catch (error) {
      console.error('❌ Error fetching preview data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch preview data');
      setPreviewData(null);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchPreviewData();
  }, []);

  // Refresh data
  const refreshData = () => {
    fetchPreviewData();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Preview Data from previewdummy.json</h1>
            <p className="text-gray-600">
              This page shows the actual data from your <code className="bg-gray-100 px-1 rounded">previewdummy.json</code> file
              with API-first integration and JSON fallback.
            </p>
          </div>

          {/* Refresh Button */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Data Controls</h2>
              <button
                onClick={refreshData}
                className="px-4 py-2 bg-[#013688] text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
              >
                <i className="ri-refresh-line mr-2"></i>
                Refresh Data
              </button>
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="animate-spin w-8 h-8 border-2 border-[#013688] border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-600">Loading preview data...</p>
            </div>
          )}

          {/* Error State */}
          {error && !loading && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <i className="ri-error-warning-line text-red-500 text-xl mr-3"></i>
                <div>
                  <h3 className="text-red-800 font-medium">Error Loading Preview Data</h3>
                  <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Data Source Verification */}
          {!loading && !error && previewData && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <i className="ri-check-line text-green-500 text-xl mr-3"></i>
                <div>
                  <h3 className="text-green-800 font-medium">✅ Preview Data Loaded Successfully</h3>
                  <p className="text-green-600 text-sm mt-1">
                    Data from <code className="bg-green-100 px-1 rounded">previewdummy.json</code>
                    • TUI: {previewData.TUI} • Status: {previewData.Code}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Main Preview Data */}
          {!loading && !error && previewData && (
            <div className="space-y-6">

              {/* Basic Information */}
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Flight Information</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-500">From</label>
                      <p className="text-lg font-semibold text-gray-900">{previewData.From}</p>
                      <p className="text-sm text-gray-600">{previewData.FromName}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">To</label>
                      <p className="text-lg font-semibold text-gray-900">{previewData.To}</p>
                      <p className="text-sm text-gray-600">{previewData.ToName}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Departure Date</label>
                      <p className="text-lg font-semibold text-gray-900">{previewData.OnwardDate}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Fare Type</label>
                      <p className="text-lg font-semibold text-gray-900">{previewData.FareType}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Fare Breakdown */}
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Fare Breakdown</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <p className="text-sm font-medium text-gray-500">Net Amount</p>
                      <p className="text-2xl font-bold text-blue-600">₹{previewData.NetAmount}</p>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <p className="text-sm font-medium text-gray-500">Gross Amount</p>
                      <p className="text-2xl font-bold text-green-600">₹{previewData.GrossAmount}</p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <p className="text-sm font-medium text-gray-500">Insurance Premium</p>
                      <p className="text-2xl font-bold text-purple-600">₹{previewData.InsPremium}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Flight Segments */}
              {previewData.Trips && previewData.Trips.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">Flight Segments</h2>
                  </div>
                  <div className="p-6">
                    {previewData.Trips.map((trip, tripIndex) => (
                      <div key={tripIndex} className="space-y-4">
                        {trip.Journey.map((journey, journeyIndex) => (
                          <div key={journeyIndex} className="border rounded-lg p-4">
                            <div className="flex items-center justify-between mb-4">
                              <div>
                                <p className="font-semibold text-gray-900">Provider: {journey.Provider}</p>
                                <p className="text-sm text-gray-600">Duration: {journey.Duration} • Stops: {journey.Stops}</p>
                              </div>
                              <div className="text-right">
                                <p className="text-lg font-bold text-gray-900">₹{journey.NetFare}</p>
                                <p className="text-sm text-gray-500">Net Fare</p>
                              </div>
                            </div>

                            {journey.Segments && journey.Segments.map((segment, segmentIndex) => (
                              <div key={segmentIndex} className="bg-gray-50 rounded-lg p-4">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  <div>
                                    <p className="font-medium text-gray-900">{segment.Flight.VAC} {segment.Flight.FlightNo}</p>
                                    <p className="text-sm text-gray-600">{segment.Flight.AirCraft}</p>
                                    <p className="text-sm text-gray-600">Class: {segment.Flight.FareClass}</p>
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-500">Departure</p>
                                    <p className="text-gray-900">{segment.Flight.DepartureCode} - Terminal {segment.Flight.DepartureTerminal}</p>
                                    <p className="text-sm text-gray-600">{new Date(segment.Flight.DepartureTime).toLocaleString()}</p>
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-500">Arrival</p>
                                    <p className="text-gray-900">{segment.Flight.ArrivalCode} - Terminal {segment.Flight.ArrivalTerminal}</p>
                                    <p className="text-sm text-gray-600">{new Date(segment.Flight.ArrivalTime).toLocaleString()}</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Technical Details */}
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Technical Details</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Transaction ID (TUI)</label>
                      <p className="text-sm font-mono text-gray-900 break-all">{previewData.TUI}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Response Code</label>
                      <p className="text-sm text-gray-900">{previewData.Code}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Messages</label>
                      <p className="text-sm text-gray-900">{previewData.Msg.join(', ')}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Elapsed Time</label>
                      <p className="text-sm text-gray-900">{previewData.ElapsedTime}s</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* No Data State */}
          {!loading && !error && !previewData && (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <i className="ri-file-list-3-line text-gray-400 text-4xl mb-4"></i>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Preview Data Found</h3>
              <p className="text-gray-600">
                Unable to load data from previewdummy.json file.
              </p>
            </div>
          )}

          {/* API Integration Info */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-blue-800 font-medium mb-2">
              <i className="ri-information-line mr-2"></i>
              API Integration Pattern
            </h3>
            <div className="text-blue-700 text-sm space-y-2">
              <p>• <strong>API-First Approach:</strong> Tries real API first, falls back to JSON when API fails</p>
              <p>• <strong>Current Data:</strong> Showing actual data from <code className="bg-blue-100 px-1 rounded">previewdummy.json</code></p>
              <p>• <strong>Data Structure:</strong> Complete flight booking response with segments, fares, rules</p>
              <p>• <strong>Integration Ready:</strong> Works with booking preview page for fare calculations</p>
              <p>• <strong>TUI Support:</strong> Handles transaction unique identifiers for API calls</p>
              <p>• <strong>Error Handling:</strong> Comprehensive error states and loading indicators</p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
