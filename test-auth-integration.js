// Simple integration test for authentication service
// This script verifies that all the authentication endpoints are properly configured

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Authentication Integration...\n');

// Test 1: Check if authService file exists and has correct exports
function testAuthServiceFile() {
  console.log('1. Testing authService.ts file...');
  
  const authServicePath = path.join(__dirname, 'app/api/services/authService.ts');
  
  if (!fs.existsSync(authServicePath)) {
    console.log('❌ authService.ts file not found');
    return false;
  }
  
  const content = fs.readFileSync(authServicePath, 'utf8');
  
  // Check for required endpoints
  const requiredMethods = [
    'login',
    'logout', 
    'refreshToken',
    'sendOTP'
  ];
  
  const requiredInterfaces = [
    'LoginRequest',
    'LoginResponse',
    'LogoutRequest', 
    'LogoutResponse',
    'RefreshTokenRequest',
    'RefreshTokenResponse'
  ];
  
  let allMethodsFound = true;
  let allInterfacesFound = true;
  
  requiredMethods.forEach(method => {
    if (!content.includes(`async ${method}(`)) {
      console.log(`❌ Method ${method} not found`);
      allMethodsFound = false;
    }
  });
  
  requiredInterfaces.forEach(interface => {
    if (!content.includes(`interface ${interface}`)) {
      console.log(`❌ Interface ${interface} not found`);
      allInterfacesFound = false;
    }
  });
  
  // Check for correct API endpoints
  const endpoints = [
    '/auth/login',
    '/auth/logout', 
    '/auth/refresh'
  ];
  
  let allEndpointsFound = true;
  endpoints.forEach(endpoint => {
    if (!content.includes(`'${endpoint}'`)) {
      console.log(`❌ Endpoint ${endpoint} not found`);
      allEndpointsFound = false;
    }
  });
  
  if (allMethodsFound && allInterfacesFound && allEndpointsFound) {
    console.log('✅ authService.ts file is properly configured');
    return true;
  }
  
  return false;
}

// Test 2: Check if auth types are updated
function testAuthTypes() {
  console.log('\n2. Testing auth types...');
  
  const authTypesPath = path.join(__dirname, 'types/auth.ts');
  
  if (!fs.existsSync(authTypesPath)) {
    console.log('❌ auth.ts types file not found');
    return false;
  }
  
  const content = fs.readFileSync(authTypesPath, 'utf8');
  
  // Check for API compatibility fields
  const requiredFields = [
    'profile_picture?',
    'is_verified?',
    'created_at?',
    'last_login?',
    'LoginApiRequest',
    'RefreshTokenApiResponse'
  ];
  
  let allFieldsFound = true;
  requiredFields.forEach(field => {
    if (!content.includes(field)) {
      console.log(`❌ Field/Interface ${field} not found`);
      allFieldsFound = false;
    }
  });
  
  if (allFieldsFound) {
    console.log('✅ Auth types are properly updated');
    return true;
  }
  
  return false;
}

// Test 3: Check if AuthContext is updated
function testAuthContext() {
  console.log('\n3. Testing AuthContext integration...');
  
  const authContextPath = path.join(__dirname, 'contexts/AuthContext.tsx');
  
  if (!fs.existsSync(authContextPath)) {
    console.log('❌ AuthContext.tsx file not found');
    return false;
  }
  
  const content = fs.readFileSync(authContextPath, 'utf8');
  
  // Check for authService import
  if (!content.includes('import { authService }')) {
    console.log('❌ authService import not found');
    return false;
  }
  
  // Check for storageUtils import
  if (!content.includes('import { storageUtils }')) {
    console.log('❌ storageUtils import not found');
    return false;
  }
  
  // Check for API calls instead of mock implementations
  if (content.includes('await new Promise(resolve => setTimeout(resolve')) {
    console.log('❌ Mock implementations still present');
    return false;
  }
  
  // Check for new methods
  const requiredMethods = [
    'loginWithCredentials',
    'refreshToken'
  ];
  
  let allMethodsFound = true;
  requiredMethods.forEach(method => {
    if (!content.includes(method)) {
      console.log(`❌ Method ${method} not found in AuthContext`);
      allMethodsFound = false;
    }
  });
  
  if (allMethodsFound) {
    console.log('✅ AuthContext is properly integrated with authService');
    return true;
  }
  
  return false;
}

// Test 4: Check storage utils integration
function testStorageUtils() {
  console.log('\n4. Testing storage utils...');
  
  const storageUtilsPath = path.join(__dirname, 'app/api/utils/storageUtils.ts');
  
  if (!fs.existsSync(storageUtilsPath)) {
    console.log('❌ storageUtils.ts file not found');
    return false;
  }
  
  const content = fs.readFileSync(storageUtilsPath, 'utf8');
  
  // Check for required methods
  const requiredMethods = [
    'getAccessToken',
    'getRefreshToken',
    'setAccessToken',
    'setRefreshToken',
    'updateTokens',
    'clearAuthData',
    'isAuthenticated'
  ];
  
  let allMethodsFound = true;
  requiredMethods.forEach(method => {
    if (!content.includes(`${method}(`)) {
      console.log(`❌ Method ${method} not found in storageUtils`);
      allMethodsFound = false;
    }
  });
  
  if (allMethodsFound) {
    console.log('✅ Storage utils are properly configured');
    return true;
  }
  
  return false;
}

// Run all tests
function runIntegrationTests() {
  const test1 = testAuthServiceFile();
  const test2 = testAuthTypes();
  const test3 = testAuthContext();
  const test4 = testStorageUtils();
  
  console.log('\n📊 Integration Test Results:');
  console.log('================================');
  
  if (test1 && test2 && test3 && test4) {
    console.log('🎉 All authentication integration tests passed!');
    console.log('\n✅ Authentication API integration is properly configured with:');
    console.log('   • POST /auth/login endpoint');
    console.log('   • POST /auth/logout endpoint'); 
    console.log('   • POST /auth/refresh endpoint');
    console.log('   • Proper error handling');
    console.log('   • Token storage management');
    console.log('   • Type safety with TypeScript interfaces');
    console.log('\n🚀 Ready for production use!');
    return true;
  } else {
    console.log('❌ Some integration tests failed. Please review the issues above.');
    return false;
  }
}

// Execute tests
runIntegrationTests();
