'use client';

import { useState } from 'react';
import { airportService } from '../../api/services/airportService';

export default function AirportDebug() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testConnectivity = async () => {
    setLoading(true);
    setResult('Testing connectivity...\n');

    try {
      // Test basic connectivity to the server
      await fetch('http://192.168.0.128:8001', {
        method: 'GET',
        mode: 'no-cors', // This will help with CORS issues for basic connectivity test
      });

      setResult(prev => prev + `Server connectivity: OK\n`);
    } catch (error) {
      setResult(prev => prev + `Server connectivity: FAILED - ${error}\n`);
    } finally {
      setLoading(false);
    }
  };

  const testAPI = async () => {
    setLoading(true);
    setResult('Testing API...\n');

    try {
      // Check environment variables
      setResult(prev => prev + `Environment check:\n`);
      setResult(prev => prev + `NEXT_PUBLIC_API_BASE_URL: ${process.env.NEXT_PUBLIC_API_BASE_URL || 'undefined'}\n`);
      setResult(prev => prev + `Default URL: http://192.168.0.128:8001\n\n`);

      // Test direct fetch to API
      setResult(prev => prev + `Testing direct fetch...\n`);
      const directResponse = await fetch('http://192.168.0.128:8001/airports/search', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      setResult(prev => prev + `Direct fetch status: ${directResponse.status} ${directResponse.statusText}\n`);

      if (directResponse.ok) {
        const directData = await directResponse.json();
        setResult(prev => prev + `Direct fetch result: ${JSON.stringify(directData, null, 2)}\n\n`);
      } else {
        setResult(prev => prev + `Direct fetch failed: ${directResponse.status}\n\n`);
      }

      // Test through service
      setResult(prev => prev + `Testing through service...\n`);
      const serviceData = await airportService.getAirports();
      setResult(prev => prev + `Service result: ${JSON.stringify(serviceData, null, 2)}\n`);

    } catch (error) {
      setResult(prev => prev + `Error: ${error}\n`);
      console.error('Debug test error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg m-4">
      <h3 className="text-lg font-bold mb-4">Airport API Debug</h3>
      
      <div className="space-x-2 mb-4">
        <button
          onClick={testConnectivity}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Connectivity'}
        </button>

        <button
          onClick={testAPI}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Airport API'}
        </button>
      </div>
      
      {result && (
        <pre className="bg-white p-4 rounded border text-sm overflow-auto max-h-96">
          {result}
        </pre>
      )}
    </div>
  );
}
