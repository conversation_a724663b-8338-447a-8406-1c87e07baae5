import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { storageUtils } from '../utils/storageUtils';

// Interface for the token refresh response
interface TokenRefreshResponse {
  access_token: string;
  refresh_token?: string;
}

// Interface for the refresh request payload
interface RefreshTokenRequest {
  refresh_token: string;
}

// Create an Axios instance with a base URL
const api: AxiosInstance = axios.create({
  baseURL: 'http://192.168.0.104:8001'
  //  baseURL: process.env.REACT_APP_AUTH_API_BASE_URL || process.env.REACT_APP_API_BASE_URL || 'http://157.245.193.55:8001'
});

// Note: JWT validation is now handled by storageUtils.isValidJWT

// Request interceptor
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token: string | null = storageUtils.getAccessToken();

    if (storageUtils.isValidJWT(token)) {
      config.headers.Authorization = `Bearer ${token}`;
    } else if (token) {
      // Clear invalid token from both storages
      storageUtils.clearAuthData();
    }

    // Only set Content-Type to JSON if it's not FormData and doesn't include /json/
    if (!config.url?.includes('/json/') && !(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json';
    }

    // For FormData, let the browser set the Content-Type with boundary
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    return config;
  },
  (error: AxiosError) => Promise.reject(error)
);

// Create a separate axios instance for auth requests to prevent infinite loops
const authApi: AxiosInstance = axios.create({
  baseURL: 'http://192.168.205.184:8001'
  //  baseURL: process.env.REACT_APP_AUTH_API_BASE_URL || process.env.REACT_APP_API_BASE_URL || 'http://157.245.193.55:8001'
});

// Extended InternalAxiosRequestConfig to include retry flag
interface ExtendedInternalAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as ExtendedInternalAxiosRequestConfig;

    // Prevent infinite retry loops
    if (originalRequest._retry) {
      return Promise.reject(error);
    }

    if (error.response && (error.response.status === 403 || error.response.status === 401)) {
      originalRequest._retry = true;

      try {
        // Get the refresh token from storage
        const refreshToken: string | null = storageUtils.getRefreshToken();

        if (!refreshToken) {
          // No refresh token available, redirect to login
          storageUtils.clearAuthData();
          // window.location.href = '/';
          return Promise.reject(error);
        }

        // Call your refresh token endpoint with the separate instance
        const response: AxiosResponse<TokenRefreshResponse> = await authApi.post<TokenRefreshResponse>(
          '/auth/refresh',
          {
            refresh_token: refreshToken
          } as RefreshTokenRequest
        );

        // If successful, update the tokens
        if (response.data && response.data.access_token) {
          storageUtils.updateTokens(response.data.access_token, response.data.refresh_token);

          // Update the auth header and retry
          originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;
          return api(originalRequest);
        } else {
          // Invalid response format
          throw new Error('Invalid token response');
        }
      } catch (refreshError) {
        // If refresh fails, clear tokens and redirect
        storageUtils.clearAuthData();
        // window.location.href = '/';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default api;