import api from './axiosinstance';

// Interface for airport data from API
export interface Airport {
  id: string;
  airport_name: string;
  airport_code: string;
  city_name: string;
  city_code: string;
  country: string;
  latitude: number | null;
  longitude: number | null;
  timezone: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Interface for airport selector component (transformed data)
export interface AirportSelectorData {
  code: string;
  name: string;
  fullName: string;
  id?: string;
  country?: string;
  latitude?: number | null;
  longitude?: number | null;
  timezone?: string | null;
}

// Transform API airport data to component format
export const transformAirportData = (airport: Airport): AirportSelectorData => {
  return {
    code: airport.airport_code,
    name: airport.city_name,
    fullName: `${airport.airport_code}, ${airport.airport_name}`,
    id: airport.id,
    country: airport.country,
    latitude: airport.latitude,
    longitude: airport.longitude,
    timezone: airport.timezone
  };
};

// LocalStorage key for caching airports
const AIRPORTS_CACHE_KEY = 'ecogo_airports_cache';
const CACHE_EXPIRY_KEY = 'ecogo_airports_cache_expiry';
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Check if cache is valid
const isCacheValid = (): boolean => {
  if (typeof window === 'undefined') return false;
  const expiry = localStorage.getItem(CACHE_EXPIRY_KEY);
  if (!expiry) return false;
  return Date.now() < parseInt(expiry);
};

// Get airports from localStorage
const getAirportsFromCache = (): AirportSelectorData[] | null => {
  if (typeof window === 'undefined') return null;

  try {
    if (!isCacheValid()) {
      // Clear expired cache
      localStorage.removeItem(AIRPORTS_CACHE_KEY);
      localStorage.removeItem(CACHE_EXPIRY_KEY);
      return null;
    }

    const cached = localStorage.getItem(AIRPORTS_CACHE_KEY);
    return cached ? JSON.parse(cached) : null;
  } catch (error) {
    console.error('Error reading airports from cache:', error);
    return null;
  }
};

// Save airports to localStorage
const saveAirportsToCache = (airports: AirportSelectorData[]): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(AIRPORTS_CACHE_KEY, JSON.stringify(airports));
    localStorage.setItem(CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());
    console.log('Airports saved to localStorage cache');
  } catch (error) {
    console.error('Error saving airports to cache:', error);
  }
};

// Airport API service
export const airportService = {
  // Get all airports with localStorage caching
  async getAirports(): Promise<AirportSelectorData[]> {
    try {
      // First, check localStorage cache
      console.log('Checking localStorage for cached airports...');
      const cachedAirports = getAirportsFromCache();

      if (cachedAirports && cachedAirports.length > 0) {
        console.log('Found cached airports:', cachedAirports.length);
        return cachedAirports;
      }

      // If not in cache, fetch from API
      console.log('No cached airports found, fetching from API: /airports/');
      const response = await api.get<Airport[]>('/airports/');

      // Validate response data
      if (!response.data || !Array.isArray(response.data)) {
        console.error('Invalid response data:', response.data);
        throw new Error('Invalid response format from airports API');
      }

      const transformedData = response.data
        .filter((airport: Airport) => airport.is_active)
        .map(transformAirportData);

      // Save to localStorage cache
      saveAirportsToCache(transformedData);

      console.log('Fetched and cached airports:', transformedData.length);
      return transformedData;
    } catch (error) {
      console.error('Error fetching airports:', error);
      throw error;
    }
  },

  // Search airports by query - always calls API for real-time results
  async searchAirports(query: string): Promise<AirportSelectorData[]> {
    try {
      // If query is empty, return cached airports
      if (!query.trim()) {
        return this.getAirports();
      }

      console.log('Searching airports with query:', query);
      const response = await api.get<Airport[]>(`/airports/search?q=${encodeURIComponent(query)}`);

      // Validate response data
      if (!response.data || !Array.isArray(response.data)) {
        throw new Error('Invalid response format from airports search API');
      }

      const searchResults = response.data
        .filter((airport: Airport) => airport.is_active)
        .map(transformAirportData);

      console.log('Search results:', searchResults.length);
      return searchResults;
    } catch (error) {
      console.error('Error searching airports:', error);
      throw error;
    }
  },

  // Get airport by code
  async getAirportByCode(code: string): Promise<AirportSelectorData | null> {
    try {
      const airports = await this.getAirports();
      const foundAirport = airports.find(airport => airport.code === code);

      if (foundAirport) {
        console.log(`✅ Found airport ${code}:`, foundAirport);
        return foundAirport;
      }

      console.warn(`⚠️ Airport ${code} not found in API data, checking fallback...`);

      // Fallback to hardcoded airport data for common airports
      const fallbackAirports: AirportSelectorData[] = [
        // Indian Airports
        { code: 'DEL', name: 'Delhi', fullName: 'Indira Gandhi International Airport', country: 'India' },
        { code: 'BLR', name: 'Bengaluru', fullName: 'Bengaluru International Airport', country: 'India' },
        { code: 'BOM', name: 'Mumbai', fullName: 'Chhatrapati Shivaji Mumbai International Airport', country: 'India' },
        { code: 'MAA', name: 'Chennai', fullName: 'Chennai International Airport', country: 'India' },
        { code: 'CCU', name: 'Kolkata', fullName: 'Netaji Subhash Chandra Bose International Airport', country: 'India' },
        { code: 'HYD', name: 'Hyderabad', fullName: 'Rajiv Gandhi International Airport', country: 'India' },
        { code: 'AMD', name: 'Ahmedabad', fullName: 'Sardar Vallabhbhai Patel International Airport', country: 'India' },
        { code: 'PNQ', name: 'Pune', fullName: 'Pune Airport', country: 'India' },
        { code: 'GOI', name: 'Goa', fullName: 'Goa International Airport', country: 'India' },
        { code: 'JAI', name: 'Jaipur', fullName: 'Jaipur International Airport', country: 'India' },
        { code: 'LKO', name: 'Lucknow', fullName: 'Chaudhary Charan Singh International Airport', country: 'India' },
        { code: 'IXC', name: 'Chandigarh', fullName: 'Chandigarh Airport', country: 'India' },
        { code: 'COK', name: 'Kochi', fullName: 'Cochin International Airport', country: 'India' },
        { code: 'TRV', name: 'Thiruvananthapuram', fullName: 'Trivandrum International Airport', country: 'India' },
        { code: 'CJB', name: 'Coimbatore', fullName: 'Coimbatore International Airport', country: 'India' },
        { code: 'MDU', name: 'Madurai', fullName: 'Madurai Airport', country: 'India' },
        { code: 'IXM', name: 'Madurai', fullName: 'Madurai Airport', country: 'India' },
        { code: 'VGA', name: 'Vijayawada', fullName: 'Vijayawada Airport', country: 'India' },
        { code: 'VTZ', name: 'Visakhapatnam', fullName: 'Visakhapatnam Airport', country: 'India' },
        { code: 'NAG', name: 'Nagpur', fullName: 'Dr. Babasaheb Ambedkar International Airport', country: 'India' },
        { code: 'RPR', name: 'Raipur', fullName: 'Swami Vivekananda Airport', country: 'India' },
        { code: 'BBI', name: 'Bhubaneswar', fullName: 'Biju Patnaik International Airport', country: 'India' },
        { code: 'GAU', name: 'Guwahati', fullName: 'Lokpriya Gopinath Bordoloi International Airport', country: 'India' },
        { code: 'IXB', name: 'Bagdogra', fullName: 'Bagdogra Airport', country: 'India' },
        { code: 'IXS', name: 'Silchar', fullName: 'Silchar Airport', country: 'India' },
        { code: 'IMF', name: 'Imphal', fullName: 'Imphal Airport', country: 'India' },
        { code: 'AJL', name: 'Aizawl', fullName: 'Lengpui Airport', country: 'India' },
        { code: 'IXA', name: 'Agartala', fullName: 'Maharaja Bir Bikram Airport', country: 'India' },
        { code: 'DIB', name: 'Dibrugarh', fullName: 'Dibrugarh Airport', country: 'India' },
        { code: 'JRH', name: 'Jorhat', fullName: 'Jorhat Airport', country: 'India' },
        { code: 'TEZ', name: 'Tezpur', fullName: 'Tezpur Airport', country: 'India' },
        { code: 'IXU', name: 'Aurangabad', fullName: 'Aurangabad Airport', country: 'India' },
        { code: 'IXN', name: 'Khajuraho', fullName: 'Khajuraho Airport', country: 'India' },
        { code: 'JLR', name: 'Jabalpur', fullName: 'Jabalpur Airport', country: 'India' },
        { code: 'IDR', name: 'Indore', fullName: 'Devi Ahilya Bai Holkar Airport', country: 'India' },
        { code: 'BHO', name: 'Bhopal', fullName: 'Raja Bhoj Airport', country: 'India' },
        { code: 'GWL', name: 'Gwalior', fullName: 'Gwalior Airport', country: 'India' },

        // US Airports - Chicago
        { code: 'ORD', name: 'Chicago', fullName: 'Chicago O\'Hare International Airport', country: 'United States' },
        { code: 'MDW', name: 'Chicago', fullName: 'Chicago Midway International Airport', country: 'United States' },

        // US Airports - New York
        { code: 'JFK', name: 'New York', fullName: 'John F. Kennedy International Airport', country: 'United States' },
        { code: 'LGA', name: 'New York', fullName: 'LaGuardia Airport', country: 'United States' },
        { code: 'EWR', name: 'New York', fullName: 'Newark Liberty International Airport', country: 'United States' },

        // Other US Major Airports
        { code: 'LAX', name: 'Los Angeles', fullName: 'Los Angeles International Airport', country: 'United States' },
        { code: 'MIA', name: 'Miami', fullName: 'Miami International Airport', country: 'United States' },
        { code: 'SFO', name: 'San Francisco', fullName: 'San Francisco International Airport', country: 'United States' },
        { code: 'SEA', name: 'Seattle', fullName: 'Seattle-Tacoma International Airport', country: 'United States' },
        { code: 'DEN', name: 'Denver', fullName: 'Denver International Airport', country: 'United States' },
        { code: 'ATL', name: 'Atlanta', fullName: 'Hartsfield-Jackson Atlanta International Airport', country: 'United States' },
        { code: 'DFW', name: 'Dallas', fullName: 'Dallas/Fort Worth International Airport', country: 'United States' },
        { code: 'IAH', name: 'Houston', fullName: 'George Bush Intercontinental Airport', country: 'United States' },
        { code: 'PHX', name: 'Phoenix', fullName: 'Phoenix Sky Harbor International Airport', country: 'United States' },
        { code: 'LAS', name: 'Las Vegas', fullName: 'McCarran International Airport', country: 'United States' },
        { code: 'BOS', name: 'Boston', fullName: 'Logan International Airport', country: 'United States' },
        { code: 'MSP', name: 'Minneapolis', fullName: 'Minneapolis-Saint Paul International Airport', country: 'United States' },
        { code: 'DTW', name: 'Detroit', fullName: 'Detroit Metropolitan Wayne County Airport', country: 'United States' },
        { code: 'PHL', name: 'Philadelphia', fullName: 'Philadelphia International Airport', country: 'United States' },
        { code: 'CLT', name: 'Charlotte', fullName: 'Charlotte Douglas International Airport', country: 'United States' },
        { code: 'MCO', name: 'Orlando', fullName: 'Orlando International Airport', country: 'United States' },
        { code: 'BWI', name: 'Baltimore', fullName: 'Baltimore/Washington International Thurgood Marshall Airport', country: 'United States' },
        { code: 'DCA', name: 'Washington', fullName: 'Ronald Reagan Washington National Airport', country: 'United States' },
        { code: 'IAD', name: 'Washington', fullName: 'Washington Dulles International Airport', country: 'United States' },

        // International Airports
        { code: 'DXB', name: 'Dubai', fullName: 'Dubai International Airport', country: 'United Arab Emirates' },
        { code: 'SIN', name: 'Singapore', fullName: 'Singapore Changi Airport', country: 'Singapore' },
        { code: 'LHR', name: 'London', fullName: 'London Heathrow Airport', country: 'United Kingdom' },
        { code: 'CDG', name: 'Paris', fullName: 'Charles de Gaulle Airport', country: 'France' },
        { code: 'FRA', name: 'Frankfurt', fullName: 'Frankfurt Airport', country: 'Germany' },
        { code: 'AMS', name: 'Amsterdam', fullName: 'Amsterdam Airport Schiphol', country: 'Netherlands' },
        { code: 'IST', name: 'Istanbul', fullName: 'Istanbul Airport', country: 'Turkey' },
        { code: 'DOH', name: 'Doha', fullName: 'Hamad International Airport', country: 'Qatar' },
        { code: 'HKG', name: 'Hong Kong', fullName: 'Hong Kong International Airport', country: 'Hong Kong' },
        { code: 'NRT', name: 'Tokyo', fullName: 'Narita International Airport', country: 'Japan' },
        { code: 'ICN', name: 'Seoul', fullName: 'Incheon International Airport', country: 'South Korea' },
        { code: 'PEK', name: 'Beijing', fullName: 'Beijing Capital International Airport', country: 'China' },
        { code: 'PVG', name: 'Shanghai', fullName: 'Shanghai Pudong International Airport', country: 'China' },
        { code: 'SYD', name: 'Sydney', fullName: 'Sydney Kingsford Smith Airport', country: 'Australia' },
        { code: 'MEL', name: 'Melbourne', fullName: 'Melbourne Airport', country: 'Australia' },
        { code: 'YYZ', name: 'Toronto', fullName: 'Toronto Pearson International Airport', country: 'Canada' },
        { code: 'YVR', name: 'Vancouver', fullName: 'Vancouver International Airport', country: 'Canada' }
      ];

      const fallbackAirport = fallbackAirports.find(airport => airport.code === code);

      if (fallbackAirport) {
        console.log(`✅ Found fallback airport ${code}:`, fallbackAirport);
        return fallbackAirport;
      }

      console.error(`❌ Airport ${code} not found in API or fallback data`);
      return null;
    } catch (error) {
      console.error('Error getting airport by code:', error);

      // Final fallback for common airports even if everything fails
      const emergencyFallbacks: { [key: string]: AirportSelectorData } = {
        // Indian Airports
        'DEL': { code: 'DEL', name: 'Delhi', fullName: 'Indira Gandhi International Airport', country: 'India' },
        'BOM': { code: 'BOM', name: 'Mumbai', fullName: 'Chhatrapati Shivaji Mumbai International Airport', country: 'India' },
        'BLR': { code: 'BLR', name: 'Bengaluru', fullName: 'Bengaluru International Airport', country: 'India' },
        'MAA': { code: 'MAA', name: 'Chennai', fullName: 'Chennai International Airport', country: 'India' },
        'COK': { code: 'COK', name: 'Kochi', fullName: 'Cochin International Airport', country: 'India' },
        'HYD': { code: 'HYD', name: 'Hyderabad', fullName: 'Rajiv Gandhi International Airport', country: 'India' },
        'CCU': { code: 'CCU', name: 'Kolkata', fullName: 'Netaji Subhash Chandra Bose International Airport', country: 'India' },
        'AMD': { code: 'AMD', name: 'Ahmedabad', fullName: 'Sardar Vallabhbhai Patel International Airport', country: 'India' },
        'PNQ': { code: 'PNQ', name: 'Pune', fullName: 'Pune Airport', country: 'India' },
        'GOI': { code: 'GOI', name: 'Goa', fullName: 'Goa International Airport', country: 'India' },

        // US Airports - Chicago & New York
        'ORD': { code: 'ORD', name: 'Chicago', fullName: 'Chicago O\'Hare International Airport', country: 'United States' },
        'MDW': { code: 'MDW', name: 'Chicago', fullName: 'Chicago Midway International Airport', country: 'United States' },
        'JFK': { code: 'JFK', name: 'New York', fullName: 'John F. Kennedy International Airport', country: 'United States' },
        'LGA': { code: 'LGA', name: 'New York', fullName: 'LaGuardia Airport', country: 'United States' },
        'EWR': { code: 'EWR', name: 'New York', fullName: 'Newark Liberty International Airport', country: 'United States' },

        // Other Major US Airports
        'LAX': { code: 'LAX', name: 'Los Angeles', fullName: 'Los Angeles International Airport', country: 'United States' },
        'MIA': { code: 'MIA', name: 'Miami', fullName: 'Miami International Airport', country: 'United States' },
        'SFO': { code: 'SFO', name: 'San Francisco', fullName: 'San Francisco International Airport', country: 'United States' },
        'ATL': { code: 'ATL', name: 'Atlanta', fullName: 'Hartsfield-Jackson Atlanta International Airport', country: 'United States' },
        'DFW': { code: 'DFW', name: 'Dallas', fullName: 'Dallas/Fort Worth International Airport', country: 'United States' }
      };

      if (emergencyFallbacks[code]) {
        console.log(`🆘 Using emergency fallback for ${code}`);
        return emergencyFallbacks[code];
      }

      throw error;
    }
  }
};

export default airportService;
